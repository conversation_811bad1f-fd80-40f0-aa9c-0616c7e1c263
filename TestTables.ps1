# Test Database Tables
Write-Host "Testing Database Tables" -ForegroundColor Green

try {
    $connectionString = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=" + (Get-Location).Path + "\Factory_Accounting_System.accdb"
    $connection = New-Object -ComObject ADODB.Connection
    $connection.Open($connectionString)
    
    Write-Host "Database connection successful" -ForegroundColor Green
    
    # Test expected tables
    $expectedTables = @(
        "Units", "Currencies", "Categories", "RawMaterials", "Suppliers",
        "PurchaseInvoices", "PurchaseInvoiceDetails", "Products", "ProductRecipes",
        "ProductionStages", "ProductionOrders", "RawMaterialsInventory",
        "FinishedProductsInventory", "InventoryMovements", "CostCenters", "ProductionCosts"
    )
    
    $existingTables = 0
    $missingTables = 0
    
    foreach ($tableName in $expectedTables) {
        try {
            $recordset = New-Object -ComObject ADODB.Recordset
            $recordset.Open("SELECT TOP 1 * FROM [$tableName]", $connection)
            $fieldCount = $recordset.Fields.Count
            $recordset.Close()
            
            Write-Host "✓ $tableName ($fieldCount fields)" -ForegroundColor Green
            $existingTables++
        }
        catch {
            Write-Host "✗ $tableName missing" -ForegroundColor Red
            $missingTables++
        }
    }
    
    Write-Host "`nSummary:" -ForegroundColor Cyan
    Write-Host "Existing tables: $existingTables" -ForegroundColor Green
    Write-Host "Missing tables: $missingTables" -ForegroundColor Red
    
    $connection.Close()
}
catch {
    Write-Host "Database test failed: $($_.Exception.Message)" -ForegroundColor Red
}
