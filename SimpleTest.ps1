# Simple Database Test Script
Write-Host "Factory Accounting System Test" -ForegroundColor Green

# Check if database file exists
if (Test-Path "Factory_Accounting_System.accdb") {
    Write-Host "Database file found" -ForegroundColor Green
    $dbFile = Get-Item "Factory_Accounting_System.accdb"
    $sizeKB = [math]::Round($dbFile.Length / 1KB, 2)
    Write-Host "File size: $sizeKB KB" -ForegroundColor Cyan
} else {
    Write-Host "Database file not found" -ForegroundColor Red
    exit
}

# Test database connection
Write-Host "Testing database connection..." -ForegroundColor Yellow

try {
    $connectionString = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=" + (Get-Location).Path + "\Factory_Accounting_System.accdb"
    $connection = New-Object -ComObject ADODB.Connection
    $connection.Open($connectionString)
    
    Write-Host "Connection successful" -ForegroundColor Green
    
    # Get table list
    $recordset = New-Object -ComObject ADODB.Recordset
    $recordset.Open("SELECT Name FROM MSysObjects WHERE Type=1 AND Flags=0", $connection)
    
    $tableCount = 0
    Write-Host "Tables found:" -ForegroundColor Cyan
    while (-not $recordset.EOF) {
        $tableName = $recordset.Fields.Item("Name").Value
        if (-not $tableName.StartsWith("MSys")) {
            Write-Host "  $tableName" -ForegroundColor White
            $tableCount++
        }
        $recordset.MoveNext()
    }
    $recordset.Close()
    
    Write-Host "Total tables: $tableCount" -ForegroundColor Green
    
    $connection.Close()
    Write-Host "Test completed successfully" -ForegroundColor Green
}
catch {
    Write-Host "Connection failed: $($_.Exception.Message)" -ForegroundColor Red
}
