كود VBA لإنشاء النماذج العربية في Microsoft Access
=====================================================

يجب نسخ هذا الكود في وحدة VBA جديدة في Access:

' إنشاء جميع النماذج العربية
Sub CreateAllArabicForms()
    ' إنشاء النماذج الأساسية
    CreateSuppliersFormArabic
    CreateRawMaterialsFormArabic
    CreatePurchaseInvoicesFormArabic
    CreateProductsFormArabic
    CreateProductionOrdersFormArabic
    CreateInventoryFormArabic
    
    ' إنشاء النموذج الرئيسي للتنقل
    CreateMainNavigationForm
    
    MsgBox "تم إنشاء جميع النماذج العربية بنجاح!", vbInformation, "نظام محاسبة المصنع"
End Sub

' نموذج إدارة الموردين
Sub CreateSuppliersFormArabic()
    Dim frm As Form
    Set frm = CreateForm()
    
    ' خصائص النموذج الأساسية
    frm.Caption = "إدارة الموردين - نظام محاسبة مصنع المعمول والمخللات"
    frm.RecordSource = "Suppliers"
    frm.DefaultView = 0 ' Single Form
    frm.AllowAdditions = True
    frm.AllowDeletions = True
    frm.AllowEdits = True
    frm.NavigationButtons = True
    frm.RecordSelectors = True
    frm.DividingLines = True
    
    ' ضبط الخصائص للغة العربية
    frm.RightToLeft = True
    frm.RightToLeftLayout = True
    
    ' إضافة العناصر للنموذج
    Dim ctl As Control
    Dim intTop As Integer
    intTop = 500
    
    ' عنوان النموذج
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, intTop, 8000, 600)
    ctl.Caption = "بيانات الموردين"
    ctl.FontSize = 16
    ctl.FontBold = True
    ctl.TextAlign = 2 ' Center
    ctl.BackColor = RGB(0, 120, 215)
    ctl.ForeColor = RGB(255, 255, 255)
    intTop = intTop + 800
    
    ' حقل اسم المورد
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, intTop, 2000, 300)
    ctl.Caption = "اسم المورد:"
    ctl.FontBold = True
    Set ctl = CreateControl(frm.Name, acTextBox, , , "SupplierName", 2600, intTop, 4000, 300)
    ctl.Name = "txtSupplierName"
    intTop = intTop + 500
    
    ' حقل جهة الاتصال
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, intTop, 2000, 300)
    ctl.Caption = "جهة الاتصال:"
    ctl.FontBold = True
    Set ctl = CreateControl(frm.Name, acTextBox, , , "ContactPerson", 2600, intTop, 4000, 300)
    ctl.Name = "txtContactPerson"
    intTop = intTop + 500
    
    ' حقل الهاتف
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, intTop, 2000, 300)
    ctl.Caption = "رقم الهاتف:"
    ctl.FontBold = True
    Set ctl = CreateControl(frm.Name, acTextBox, , , "Phone", 2600, intTop, 2000, 300)
    ctl.Name = "txtPhone"
    
    ' حقل الجوال
    Set ctl = CreateControl(frm.Name, acLabel, , , , 4800, intTop, 1500, 300)
    ctl.Caption = "الجوال:"
    ctl.FontBold = True
    Set ctl = CreateControl(frm.Name, acTextBox, , , "Mobile", 6400, intTop, 2000, 300)
    ctl.Name = "txtMobile"
    intTop = intTop + 500
    
    ' حقل البريد الإلكتروني
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, intTop, 2000, 300)
    ctl.Caption = "البريد الإلكتروني:"
    ctl.FontBold = True
    Set ctl = CreateControl(frm.Name, acTextBox, , , "Email", 2600, intTop, 4000, 300)
    ctl.Name = "txtEmail"
    intTop = intTop + 500
    
    ' حقل المدينة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, intTop, 2000, 300)
    ctl.Caption = "المدينة:"
    ctl.FontBold = True
    Set ctl = CreateControl(frm.Name, acTextBox, , , "City", 2600, intTop, 2000, 300)
    ctl.Name = "txtCity"
    
    ' حقل الدولة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 4800, intTop, 1500, 300)
    ctl.Caption = "الدولة:"
    ctl.FontBold = True
    Set ctl = CreateControl(frm.Name, acTextBox, , , "Country", 6400, intTop, 2000, 300)
    ctl.Name = "txtCountry"
    intTop = intTop + 500
    
    ' حقل شروط الدفع
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, intTop, 2000, 300)
    ctl.Caption = "شروط الدفع:"
    ctl.FontBold = True
    Set ctl = CreateControl(frm.Name, acTextBox, , , "PaymentTerms", 2600, intTop, 4000, 300)
    ctl.Name = "txtPaymentTerms"
    intTop = intTop + 500
    
    ' حقل حد الائتمان
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, intTop, 2000, 300)
    ctl.Caption = "حد الائتمان:"
    ctl.FontBold = True
    Set ctl = CreateControl(frm.Name, acTextBox, , , "CreditLimit", 2600, intTop, 2000, 300)
    ctl.Name = "txtCreditLimit"
    ctl.Format = "Currency"
    
    ' حقل رصيد الحساب
    Set ctl = CreateControl(frm.Name, acLabel, , , , 4800, intTop, 1500, 300)
    ctl.Caption = "رصيد الحساب:"
    ctl.FontBold = True
    Set ctl = CreateControl(frm.Name, acTextBox, , , "AccountBalance", 6400, intTop, 2000, 300)
    ctl.Name = "txtAccountBalance"
    ctl.Format = "Currency"
    ctl.Enabled = False
    intTop = intTop + 500
    
    ' حقل العنوان
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, intTop, 2000, 300)
    ctl.Caption = "العنوان:"
    ctl.FontBold = True
    Set ctl = CreateControl(frm.Name, acTextBox, , , "Address", 2600, intTop, 5800, 800)
    ctl.Name = "txtAddress"
    ctl.EnterKeyBehavior = True
    intTop = intTop + 1000
    
    ' حقل الملاحظات
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, intTop, 2000, 300)
    ctl.Caption = "ملاحظات:"
    ctl.FontBold = True
    Set ctl = CreateControl(frm.Name, acTextBox, , , "Notes", 2600, intTop, 5800, 800)
    ctl.Name = "txtNotes"
    ctl.EnterKeyBehavior = True
    intTop = intTop + 1000
    
    ' أزرار التحكم
    Set ctl = CreateControl(frm.Name, acCommandButton, , , , 1000, intTop, 1200, 400)
    ctl.Caption = "جديد"
    ctl.Name = "btnNew"
    
    Set ctl = CreateControl(frm.Name, acCommandButton, , , , 2400, intTop, 1200, 400)
    ctl.Caption = "حفظ"
    ctl.Name = "btnSave"
    
    Set ctl = CreateControl(frm.Name, acCommandButton, , , , 3800, intTop, 1200, 400)
    ctl.Caption = "حذف"
    ctl.Name = "btnDelete"
    
    Set ctl = CreateControl(frm.Name, acCommandButton, , , , 5200, intTop, 1200, 400)
    ctl.Caption = "إغلاق"
    ctl.Name = "btnClose"
    
    ' حفظ النموذج
    DoCmd.Save acForm, frm.Name
    DoCmd.Rename "نموذج_الموردين", acForm, frm.Name
End Sub

' نموذج إدارة المواد الخام
Sub CreateRawMaterialsFormArabic()
    Dim frm As Form
    Set frm = CreateForm()
    
    ' خصائص النموذج الأساسية
    frm.Caption = "إدارة المواد الخام - نظام محاسبة مصنع المعمول والمخللات"
    frm.RecordSource = "RawMaterials"
    frm.DefaultView = 0 ' Single Form
    frm.AllowAdditions = True
    frm.AllowDeletions = True
    frm.AllowEdits = True
    frm.NavigationButtons = True
    frm.RecordSelectors = True
    frm.DividingLines = True
    
    ' ضبط الخصائص للغة العربية
    frm.RightToLeft = True
    frm.RightToLeftLayout = True
    
    ' إضافة العناصر للنموذج
    Dim ctl As Control
    Dim intTop As Integer
    intTop = 500
    
    ' عنوان النموذج
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, intTop, 8000, 600)
    ctl.Caption = "بيانات المواد الخام"
    ctl.FontSize = 16
    ctl.FontBold = True
    ctl.TextAlign = 2 ' Center
    ctl.BackColor = RGB(0, 120, 215)
    ctl.ForeColor = RGB(255, 255, 255)
    intTop = intTop + 800
    
    ' حقل اسم المادة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, intTop, 2000, 300)
    ctl.Caption = "اسم المادة:"
    ctl.FontBold = True
    Set ctl = CreateControl(frm.Name, acTextBox, , , "MaterialName", 2600, intTop, 4000, 300)
    ctl.Name = "txtMaterialName"
    intTop = intTop + 500
    
    ' حقل الفئة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, intTop, 2000, 300)
    ctl.Caption = "الفئة:"
    ctl.FontBold = True
    Set ctl = CreateControl(frm.Name, acComboBox, , , "CategoryID", 2600, intTop, 3000, 300)
    ctl.Name = "cmbCategory"
    ctl.RowSource = "SELECT CategoryID, CategoryName FROM Categories WHERE CategoryType = 'Raw Materials' ORDER BY CategoryName"
    ctl.ColumnCount = 2
    ctl.ColumnWidths = "0;3000"
    ctl.BoundColumn = 1
    ctl.ListWidth = 3000
    intTop = intTop + 500
    
    ' حقل وحدة القياس
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, intTop, 2000, 300)
    ctl.Caption = "وحدة القياس:"
    ctl.FontBold = True
    Set ctl = CreateControl(frm.Name, acComboBox, , , "UnitID", 2600, intTop, 2000, 300)
    ctl.Name = "cmbUnit"
    ctl.RowSource = "SELECT UnitID, UnitName FROM Units ORDER BY UnitName"
    ctl.ColumnCount = 2
    ctl.ColumnWidths = "0;2000"
    ctl.BoundColumn = 1
    ctl.ListWidth = 2000
    intTop = intTop + 500
    
    ' حقل الحد الأدنى
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, intTop, 2000, 300)
    ctl.Caption = "الحد الأدنى:"
    ctl.FontBold = True
    Set ctl = CreateControl(frm.Name, acTextBox, , , "MinimumLevel", 2600, intTop, 1500, 300)
    ctl.Name = "txtMinimumLevel"
    ctl.Format = "Standard"
    
    ' حقل متوسط السعر
    Set ctl = CreateControl(frm.Name, acLabel, , , , 4300, intTop, 1500, 300)
    ctl.Caption = "متوسط السعر:"
    ctl.FontBold = True
    Set ctl = CreateControl(frm.Name, acTextBox, , , "AveragePrice", 5900, intTop, 1500, 300)
    ctl.Name = "txtAveragePrice"
    ctl.Format = "Currency"
    intTop = intTop + 500
    
    ' حقل الملاحظات
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, intTop, 2000, 300)
    ctl.Caption = "ملاحظات:"
    ctl.FontBold = True
    Set ctl = CreateControl(frm.Name, acTextBox, , , "Notes", 2600, intTop, 5800, 800)
    ctl.Name = "txtNotes"
    ctl.EnterKeyBehavior = True
    intTop = intTop + 1000
    
    ' أزرار التحكم
    Set ctl = CreateControl(frm.Name, acCommandButton, , , , 1000, intTop, 1200, 400)
    ctl.Caption = "جديد"
    ctl.Name = "btnNew"
    
    Set ctl = CreateControl(frm.Name, acCommandButton, , , , 2400, intTop, 1200, 400)
    ctl.Caption = "حفظ"
    ctl.Name = "btnSave"
    
    Set ctl = CreateControl(frm.Name, acCommandButton, , , , 3800, intTop, 1200, 400)
    ctl.Caption = "حذف"
    ctl.Name = "btnDelete"
    
    Set ctl = CreateControl(frm.Name, acCommandButton, , , , 5200, intTop, 1200, 400)
    ctl.Caption = "إغلاق"
    ctl.Name = "btnClose"
    
    ' حفظ النموذج
    DoCmd.Save acForm, frm.Name
    DoCmd.Rename "نموذج_المواد_الخام", acForm, frm.Name
End Sub

' نموذج فواتير الشراء
Sub CreatePurchaseInvoicesFormArabic()
    Dim frm As Form
    Set frm = CreateForm()

    ' خصائص النموذج الأساسية
    frm.Caption = "فواتير الشراء - نظام محاسبة مصنع المعمول والمخللات"
    frm.RecordSource = "PurchaseInvoices"
    frm.DefaultView = 0 ' Single Form
    frm.AllowAdditions = True
    frm.AllowDeletions = True
    frm.AllowEdits = True
    frm.NavigationButtons = True
    frm.RecordSelectors = True
    frm.DividingLines = True

    ' ضبط الخصائص للغة العربية
    frm.RightToLeft = True
    frm.RightToLeftLayout = True

    ' إضافة العناصر للنموذج
    Dim ctl As Control
    Dim intTop As Integer
    intTop = 500

    ' عنوان النموذج
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, intTop, 8000, 600)
    ctl.Caption = "فواتير الشراء"
    ctl.FontSize = 16
    ctl.FontBold = True
    ctl.TextAlign = 2 ' Center
    ctl.BackColor = RGB(0, 120, 215)
    ctl.ForeColor = RGB(255, 255, 255)
    intTop = intTop + 800

    ' رقم فاتورة المورد
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, intTop, 2000, 300)
    ctl.Caption = "رقم فاتورة المورد:"
    ctl.FontBold = True
    Set ctl = CreateControl(frm.Name, acTextBox, , , "SupplierInvoiceNumber", 2600, intTop, 2000, 300)
    ctl.Name = "txtSupplierInvoiceNumber"

    ' تاريخ الفاتورة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 4800, intTop, 1500, 300)
    ctl.Caption = "تاريخ الفاتورة:"
    ctl.FontBold = True
    Set ctl = CreateControl(frm.Name, acTextBox, , , "InvoiceDate", 6400, intTop, 1500, 300)
    ctl.Name = "txtInvoiceDate"
    ctl.Format = "Short Date"
    intTop = intTop + 500

    ' المورد
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, intTop, 2000, 300)
    ctl.Caption = "المورد:"
    ctl.FontBold = True
    Set ctl = CreateControl(frm.Name, acComboBox, , , "SupplierID", 2600, intTop, 4000, 300)
    ctl.Name = "cmbSupplier"
    ctl.RowSource = "SELECT SupplierID, SupplierName FROM Suppliers WHERE IsActive = True ORDER BY SupplierName"
    ctl.ColumnCount = 2
    ctl.ColumnWidths = "0;4000"
    ctl.BoundColumn = 1
    ctl.ListWidth = 4000
    intTop = intTop + 500

    ' العملة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, intTop, 2000, 300)
    ctl.Caption = "العملة:"
    ctl.FontBold = True
    Set ctl = CreateControl(frm.Name, acComboBox, , , "CurrencyID", 2600, intTop, 2000, 300)
    ctl.Name = "cmbCurrency"
    ctl.RowSource = "SELECT CurrencyID, CurrencyName FROM Currencies ORDER BY CurrencyName"
    ctl.ColumnCount = 2
    ctl.ColumnWidths = "0;2000"
    ctl.BoundColumn = 1
    ctl.ListWidth = 2000

    ' سعر الصرف
    Set ctl = CreateControl(frm.Name, acLabel, , , , 4800, intTop, 1500, 300)
    ctl.Caption = "سعر الصرف:"
    ctl.FontBold = True
    Set ctl = CreateControl(frm.Name, acTextBox, , , "ExchangeRate", 6400, intTop, 1500, 300)
    ctl.Name = "txtExchangeRate"
    ctl.Format = "Standard"
    intTop = intTop + 500

    ' إجمالي قبل الضريبة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, intTop, 2000, 300)
    ctl.Caption = "إجمالي قبل الضريبة:"
    ctl.FontBold = True
    Set ctl = CreateControl(frm.Name, acTextBox, , , "SubTotal", 2600, intTop, 1500, 300)
    ctl.Name = "txtSubTotal"
    ctl.Format = "Currency"

    ' قيمة الضريبة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 4300, intTop, 1500, 300)
    ctl.Caption = "قيمة الضريبة:"
    ctl.FontBold = True
    Set ctl = CreateControl(frm.Name, acTextBox, , , "TaxAmount", 5900, intTop, 1500, 300)
    ctl.Name = "txtTaxAmount"
    ctl.Format = "Currency"
    intTop = intTop + 500

    ' إجمالي الفاتورة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, intTop, 2000, 300)
    ctl.Caption = "إجمالي الفاتورة:"
    ctl.FontBold = True
    Set ctl = CreateControl(frm.Name, acTextBox, , , "TotalAmount", 2600, intTop, 1500, 300)
    ctl.Name = "txtTotalAmount"
    ctl.Format = "Currency"
    ctl.FontBold = True
    ctl.BackColor = RGB(255, 255, 200)

    ' المبلغ المدفوع
    Set ctl = CreateControl(frm.Name, acLabel, , , , 4300, intTop, 1500, 300)
    ctl.Caption = "المبلغ المدفوع:"
    ctl.FontBold = True
    Set ctl = CreateControl(frm.Name, acTextBox, , , "PaidAmount", 5900, intTop, 1500, 300)
    ctl.Name = "txtPaidAmount"
    ctl.Format = "Currency"
    intTop = intTop + 500

    ' حالة الفاتورة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, intTop, 2000, 300)
    ctl.Caption = "حالة الفاتورة:"
    ctl.FontBold = True
    Set ctl = CreateControl(frm.Name, acComboBox, , , "InvoiceStatus", 2600, intTop, 2000, 300)
    ctl.Name = "cmbInvoiceStatus"
    ctl.RowSourceType = "Value List"
    ctl.RowSource = "مفتوحة;مدفوعة;ملغاة"
    intTop = intTop + 500

    ' الملاحظات
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, intTop, 2000, 300)
    ctl.Caption = "ملاحظات:"
    ctl.FontBold = True
    Set ctl = CreateControl(frm.Name, acTextBox, , , "Notes", 2600, intTop, 5800, 600)
    ctl.Name = "txtNotes"
    ctl.EnterKeyBehavior = True
    intTop = intTop + 800

    ' أزرار التحكم
    Set ctl = CreateControl(frm.Name, acCommandButton, , , , 1000, intTop, 1200, 400)
    ctl.Caption = "جديد"
    ctl.Name = "btnNew"

    Set ctl = CreateControl(frm.Name, acCommandButton, , , , 2400, intTop, 1200, 400)
    ctl.Caption = "حفظ"
    ctl.Name = "btnSave"

    Set ctl = CreateControl(frm.Name, acCommandButton, , , , 3800, intTop, 1200, 400)
    ctl.Caption = "تفاصيل الفاتورة"
    ctl.Name = "btnDetails"

    Set ctl = CreateControl(frm.Name, acCommandButton, , , , 5200, intTop, 1200, 400)
    ctl.Caption = "إغلاق"
    ctl.Name = "btnClose"

    ' حفظ النموذج
    DoCmd.Save acForm, frm.Name
    DoCmd.Rename "نموذج_فواتير_الشراء", acForm, frm.Name
End Sub

' النموذج الرئيسي للتنقل
Sub CreateMainNavigationForm()
    Dim frm As Form
    Set frm = CreateForm()

    ' خصائص النموذج الأساسية
    frm.Caption = "نظام محاسبة مصنع المعمول والمخللات - القائمة الرئيسية"
    frm.DefaultView = 0 ' Single Form
    frm.AllowAdditions = False
    frm.AllowDeletions = False
    frm.AllowEdits = False
    frm.NavigationButtons = False
    frm.RecordSelectors = False
    frm.DividingLines = False
    frm.ScrollBars = 0 ' Neither
    frm.PopUp = True
    frm.Modal = False

    ' ضبط الخصائص للغة العربية
    frm.RightToLeft = True
    frm.RightToLeftLayout = True

    ' إضافة العناصر للنموذج
    Dim ctl As Control
    Dim intTop As Integer
    Dim intLeft As Integer
    intTop = 500
    intLeft = 500

    ' عنوان النظام
    Set ctl = CreateControl(frm.Name, acLabel, , , , intLeft, intTop, 10000, 800)
    ctl.Caption = "نظام محاسبة مصنع المعمول والمخللات"
    ctl.FontSize = 20
    ctl.FontBold = True
    ctl.TextAlign = 2 ' Center
    ctl.BackColor = RGB(0, 120, 215)
    ctl.ForeColor = RGB(255, 255, 255)
    intTop = intTop + 1000

    ' قسم إدارة البيانات الأساسية
    Set ctl = CreateControl(frm.Name, acLabel, , , , intLeft, intTop, 10000, 400)
    ctl.Caption = "إدارة البيانات الأساسية"
    ctl.FontSize = 14
    ctl.FontBold = True
    ctl.BackColor = RGB(240, 240, 240)
    intTop = intTop + 500

    ' أزرار البيانات الأساسية
    Set ctl = CreateControl(frm.Name, acCommandButton, , , , intLeft, intTop, 2000, 600)
    ctl.Caption = "إدارة الموردين"
    ctl.Name = "btnSuppliers"

    Set ctl = CreateControl(frm.Name, acCommandButton, , , , intLeft + 2200, intTop, 2000, 600)
    ctl.Caption = "إدارة المواد الخام"
    ctl.Name = "btnRawMaterials"

    Set ctl = CreateControl(frm.Name, acCommandButton, , , , intLeft + 4400, intTop, 2000, 600)
    ctl.Caption = "إدارة المنتجات"
    ctl.Name = "btnProducts"

    Set ctl = CreateControl(frm.Name, acCommandButton, , , , intLeft + 6600, intTop, 2000, 600)
    ctl.Caption = "إدارة المخزون"
    ctl.Name = "btnInventory"
    intTop = intTop + 800

    ' قسم العمليات
    Set ctl = CreateControl(frm.Name, acLabel, , , , intLeft, intTop, 10000, 400)
    ctl.Caption = "العمليات اليومية"
    ctl.FontSize = 14
    ctl.FontBold = True
    ctl.BackColor = RGB(240, 240, 240)
    intTop = intTop + 500

    ' أزرار العمليات
    Set ctl = CreateControl(frm.Name, acCommandButton, , , , intLeft, intTop, 2000, 600)
    ctl.Caption = "فواتير الشراء"
    ctl.Name = "btnPurchaseInvoices"

    Set ctl = CreateControl(frm.Name, acCommandButton, , , , intLeft + 2200, intTop, 2000, 600)
    ctl.Caption = "أوامر الإنتاج"
    ctl.Name = "btnProductionOrders"

    Set ctl = CreateControl(frm.Name, acCommandButton, , , , intLeft + 4400, intTop, 2000, 600)
    ctl.Caption = "حركات المخزون"
    ctl.Name = "btnInventoryMovements"

    Set ctl = CreateControl(frm.Name, acCommandButton, , , , intLeft + 6600, intTop, 2000, 600)
    ctl.Caption = "تكاليف الإنتاج"
    ctl.Name = "btnProductionCosts"
    intTop = intTop + 800

    ' قسم التقارير
    Set ctl = CreateControl(frm.Name, acLabel, , , , intLeft, intTop, 10000, 400)
    ctl.Caption = "التقارير والاستعلامات"
    ctl.FontSize = 14
    ctl.FontBold = True
    ctl.BackColor = RGB(240, 240, 240)
    intTop = intTop + 500

    ' أزرار التقارير
    Set ctl = CreateControl(frm.Name, acCommandButton, , , , intLeft, intTop, 2000, 600)
    ctl.Caption = "تقرير المخزون"
    ctl.Name = "btnInventoryReport"

    Set ctl = CreateControl(frm.Name, acCommandButton, , , , intLeft + 2200, intTop, 2000, 600)
    ctl.Caption = "تقرير الموردين"
    ctl.Name = "btnSuppliersReport"

    Set ctl = CreateControl(frm.Name, acCommandButton, , , , intLeft + 4400, intTop, 2000, 600)
    ctl.Caption = "تحليل الأرباح"
    ctl.Name = "btnProfitabilityReport"

    Set ctl = CreateControl(frm.Name, acCommandButton, , , , intLeft + 6600, intTop, 2000, 600)
    ctl.Caption = "كفاءة الإنتاج"
    ctl.Name = "btnProductionEfficiency"
    intTop = intTop + 1000

    ' زر الخروج
    Set ctl = CreateControl(frm.Name, acCommandButton, , , , intLeft + 4000, intTop, 2000, 600)
    ctl.Caption = "خروج من النظام"
    ctl.Name = "btnExit"
    ctl.BackColor = RGB(220, 50, 50)
    ctl.ForeColor = RGB(255, 255, 255)
    ctl.FontBold = True

    ' حفظ النموذج
    DoCmd.Save acForm, frm.Name
    DoCmd.Rename "القائمة_الرئيسية", acForm, frm.Name
End Sub
