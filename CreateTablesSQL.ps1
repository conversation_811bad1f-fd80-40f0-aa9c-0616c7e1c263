# Create Database Tables
Write-Host "Creating Database Tables" -ForegroundColor Green

try {
    $connectionString = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=" + (Get-Location).Path + "\Factory_Accounting_System.accdb"
    $connection = New-Object -ComObject ADODB.Connection
    $connection.Open($connectionString)
    
    Write-Host "Database connection successful" -ForegroundColor Green
    
    # Create Units table
    $sql = "CREATE TABLE Units (UnitID AUTOINCREMENT PRIMARY KEY, UnitName TEXT(50) NOT NULL, Symbol TEXT(10), Notes MEMO)"
    $connection.Execute($sql)
    Write-Host "Units table created" -ForegroundColor Green
    
    # Create Currencies table
    $sql = "CREATE TABLE Currencies (CurrencyID AUTOINCREMENT PRIMARY KEY, CurrencyName TEXT(50) NOT NULL, Symbol TEXT(10), ExchangeRate CURRENCY DEFAULT 1, UpdateDate DATETIME)"
    $connection.Execute($sql)
    Write-Host "Currencies table created" -ForegroundColor Green
    
    # Create Categories table
    $sql = "CREATE TABLE Categories (CategoryID AUTOINCREMENT PRIMARY KEY, CategoryName TEXT(100) NOT NULL, Description MEMO, CategoryType TEXT(20))"
    $connection.Execute($sql)
    Write-Host "Categories table created" -ForegroundColor Green
    
    # Create RawMaterials table
    $sql = "CREATE TABLE RawMaterials (MaterialID AUTOINCREMENT PRIMARY KEY, MaterialName TEXT(100) NOT NULL, CategoryID LONG, UnitID LONG, MinimumLevel DOUBLE DEFAULT 0, AveragePrice CURRENCY DEFAULT 0, Notes MEMO, CreatedDate DATETIME, IsActive YESNO DEFAULT True)"
    $connection.Execute($sql)
    Write-Host "RawMaterials table created" -ForegroundColor Green
    
    # Create Suppliers table
    $sql = "CREATE TABLE Suppliers (SupplierID AUTOINCREMENT PRIMARY KEY, SupplierName TEXT(100) NOT NULL, ContactPerson TEXT(100), Phone TEXT(20), Mobile TEXT(20), Email TEXT(100), Address MEMO, City TEXT(50), Country TEXT(50), TaxNumber TEXT(50), PaymentTerms TEXT(100), CreditDays INTEGER DEFAULT 0, CreditLimit CURRENCY DEFAULT 0, AccountBalance CURRENCY DEFAULT 0, RegistrationDate DATETIME, IsActive YESNO DEFAULT True, Notes MEMO)"
    $connection.Execute($sql)
    Write-Host "Suppliers table created" -ForegroundColor Green
    
    # Create PurchaseInvoices table
    $sql = "CREATE TABLE PurchaseInvoices (InvoiceID AUTOINCREMENT PRIMARY KEY, SupplierInvoiceNumber TEXT(50), SupplierID LONG NOT NULL, InvoiceDate DATETIME, DueDate DATETIME, CurrencyID LONG DEFAULT 1, ExchangeRate CURRENCY DEFAULT 1, SubTotal CURRENCY DEFAULT 0, TaxAmount CURRENCY DEFAULT 0, TaxRate DOUBLE DEFAULT 0, TotalAmount CURRENCY DEFAULT 0, PaidAmount CURRENCY DEFAULT 0, RemainingAmount CURRENCY DEFAULT 0, InvoiceStatus TEXT(20) DEFAULT 'Open', Notes MEMO, EntryDate DATETIME, EntryUser TEXT(50))"
    $connection.Execute($sql)
    Write-Host "PurchaseInvoices table created" -ForegroundColor Green
    
    # Create PurchaseInvoiceDetails table
    $sql = "CREATE TABLE PurchaseInvoiceDetails (DetailID AUTOINCREMENT PRIMARY KEY, InvoiceID LONG NOT NULL, MaterialID LONG NOT NULL, Quantity DOUBLE NOT NULL, UnitPrice CURRENCY NOT NULL, LineTotal CURRENCY, DiscountPercent DOUBLE DEFAULT 0, DiscountAmount CURRENCY DEFAULT 0, NetAmount CURRENCY, ExpiryDate DATETIME, LotNumber TEXT(50), Notes MEMO)"
    $connection.Execute($sql)
    Write-Host "PurchaseInvoiceDetails table created" -ForegroundColor Green
    
    # Create Products table
    $sql = "CREATE TABLE Products (ProductID AUTOINCREMENT PRIMARY KEY, ProductName TEXT(100) NOT NULL, CategoryID LONG, UnitID LONG, SalePrice CURRENCY DEFAULT 0, ProductionCost CURRENCY DEFAULT 0, ProductionTime INTEGER DEFAULT 0, MinimumLevel DOUBLE DEFAULT 0, Description MEMO, CreatedDate DATETIME, IsActive YESNO DEFAULT True)"
    $connection.Execute($sql)
    Write-Host "Products table created" -ForegroundColor Green
    
    # Create ProductRecipes table
    $sql = "CREATE TABLE ProductRecipes (RecipeID AUTOINCREMENT PRIMARY KEY, ProductID LONG NOT NULL, MaterialID LONG NOT NULL, RequiredQuantity DOUBLE NOT NULL, MaterialCost CURRENCY DEFAULT 0, Notes MEMO)"
    $connection.Execute($sql)
    Write-Host "ProductRecipes table created" -ForegroundColor Green
    
    # Create ProductionStages table
    $sql = "CREATE TABLE ProductionStages (StageID AUTOINCREMENT PRIMARY KEY, StageName TEXT(100) NOT NULL, Description MEMO, StageOrder INTEGER, StageCost CURRENCY DEFAULT 0, StageTime INTEGER DEFAULT 0)"
    $connection.Execute($sql)
    Write-Host "ProductionStages table created" -ForegroundColor Green
    
    # Create ProductionOrders table
    $sql = "CREATE TABLE ProductionOrders (OrderID AUTOINCREMENT PRIMARY KEY, ProductID LONG NOT NULL, RequiredQuantity DOUBLE NOT NULL, ProducedQuantity DOUBLE DEFAULT 0, OrderDate DATETIME, StartDate DATETIME, EndDate DATETIME, OrderStatus TEXT(20) DEFAULT 'New', EstimatedCost CURRENCY DEFAULT 0, ActualCost CURRENCY DEFAULT 0, Notes MEMO, EntryUser TEXT(50))"
    $connection.Execute($sql)
    Write-Host "ProductionOrders table created" -ForegroundColor Green
    
    # Create RawMaterialsInventory table
    $sql = "CREATE TABLE RawMaterialsInventory (InventoryID AUTOINCREMENT PRIMARY KEY, MaterialID LONG NOT NULL, AvailableQuantity DOUBLE DEFAULT 0, AverageCost CURRENCY DEFAULT 0, TotalValue CURRENCY DEFAULT 0, LastMovementDate DATETIME, MinimumLevel DOUBLE DEFAULT 0, MaximumLevel DOUBLE DEFAULT 0, ReorderPoint DOUBLE DEFAULT 0, Notes MEMO)"
    $connection.Execute($sql)
    Write-Host "RawMaterialsInventory table created" -ForegroundColor Green
    
    # Create FinishedProductsInventory table
    $sql = "CREATE TABLE FinishedProductsInventory (InventoryID AUTOINCREMENT PRIMARY KEY, ProductID LONG NOT NULL, AvailableQuantity DOUBLE DEFAULT 0, AverageCost CURRENCY DEFAULT 0, TotalValue CURRENCY DEFAULT 0, LastMovementDate DATETIME, MinimumLevel DOUBLE DEFAULT 0, MaximumLevel DOUBLE DEFAULT 0, ExpiryDate DATETIME, LotNumber TEXT(50), Notes MEMO)"
    $connection.Execute($sql)
    Write-Host "FinishedProductsInventory table created" -ForegroundColor Green
    
    # Create InventoryMovements table
    $sql = "CREATE TABLE InventoryMovements (MovementID AUTOINCREMENT PRIMARY KEY, MovementType TEXT(20) NOT NULL, ItemType TEXT(20) NOT NULL, ItemID LONG NOT NULL, Quantity DOUBLE NOT NULL, UnitPrice CURRENCY DEFAULT 0, TotalValue CURRENCY DEFAULT 0, MovementDate DATETIME, ReferenceNumber LONG, ReferenceType TEXT(20), BalanceBefore DOUBLE DEFAULT 0, BalanceAfter DOUBLE DEFAULT 0, Notes MEMO, EntryUser TEXT(50))"
    $connection.Execute($sql)
    Write-Host "InventoryMovements table created" -ForegroundColor Green
    
    # Create CostCenters table
    $sql = "CREATE TABLE CostCenters (CenterID AUTOINCREMENT PRIMARY KEY, CenterName TEXT(100) NOT NULL, CenterType TEXT(50), Description MEMO, IsActive YESNO DEFAULT True)"
    $connection.Execute($sql)
    Write-Host "CostCenters table created" -ForegroundColor Green
    
    # Create ProductionCosts table
    $sql = "CREATE TABLE ProductionCosts (CostID AUTOINCREMENT PRIMARY KEY, OrderID LONG NOT NULL, CenterID LONG, CostType TEXT(50), CostAmount CURRENCY NOT NULL, CostDate DATETIME, Description MEMO, EntryUser TEXT(50))"
    $connection.Execute($sql)
    Write-Host "ProductionCosts table created" -ForegroundColor Green
    
    Write-Host "All tables created successfully!" -ForegroundColor Green
    
    $connection.Close()
}
catch {
    Write-Host "Error creating tables" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Yellow
}
