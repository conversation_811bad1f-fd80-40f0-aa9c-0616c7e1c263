# Test Database Queries and Reports
Write-Host "Testing Database Queries and Reports" -ForegroundColor Green

try {
    $connectionString = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=" + (Get-Location).Path + "\Factory_Accounting_System.accdb"
    $connection = New-Object -ComObject ADODB.Connection
    $connection.Open($connectionString)
    
    Write-Host "Database connection successful" -ForegroundColor Green
    
    # Test 1: Basic data count queries
    Write-Host "`n=== Test 1: Basic Data Count Queries ===" -ForegroundColor Cyan
    
    $tables = @("Units", "Currencies", "Categories", "RawMaterials", "Suppliers", "Products", "ProductionStages", "CostCenters")
    
    foreach ($table in $tables) {
        try {
            $recordset = New-Object -ComObject ADODB.Recordset
            $recordset.Open("SELECT COUNT(*) as RecordCount FROM [$table]", $connection)
            $count = $recordset.Fields.Item("RecordCount").Value
            $recordset.Close()
            Write-Host "Table $table contains $count records" -ForegroundColor Green
        }
        catch {
            Write-Host "Error querying table $table" -ForegroundColor Red
        }
    }
    
    # Test 2: Inventory Report Query
    Write-Host "`n=== Test 2: Inventory Report Query ===" -ForegroundColor Cyan
    
    try {
        $sql = @"
SELECT 
    rm.MaterialName AS [Material Name],
    c.CategoryName AS [Category],
    u.UnitName AS [Unit],
    rm.MinimumLevel AS [Minimum Level],
    rm.AveragePrice AS [Average Price]
FROM ((RawMaterials rm 
LEFT JOIN Categories c ON rm.CategoryID = c.CategoryID) 
LEFT JOIN Units u ON rm.UnitID = u.UnitID)
WHERE rm.IsActive = True
ORDER BY c.CategoryName, rm.MaterialName
"@
        
        $recordset = New-Object -ComObject ADODB.Recordset
        $recordset.Open($sql, $connection)
        
        $recordCount = 0
        Write-Host "Raw Materials Inventory Report:" -ForegroundColor Yellow
        while (-not $recordset.EOF) {
            $materialName = $recordset.Fields.Item("Material Name").Value
            $category = $recordset.Fields.Item("Category").Value
            $unit = $recordset.Fields.Item("Unit").Value
            $minLevel = $recordset.Fields.Item("Minimum Level").Value
            $avgPrice = $recordset.Fields.Item("Average Price").Value
            
            Write-Host "  $materialName ($category) - Min: $minLevel $unit, Avg Price: $avgPrice" -ForegroundColor White
            $recordCount++
            $recordset.MoveNext()
        }
        $recordset.Close()
        Write-Host "Total materials: $recordCount" -ForegroundColor Green
    }
    catch {
        Write-Host "Error in inventory report query" -ForegroundColor Red
        Write-Host $_.Exception.Message -ForegroundColor Yellow
    }
    
    # Test 3: Suppliers Report Query
    Write-Host "`n=== Test 3: Suppliers Report Query ===" -ForegroundColor Cyan
    
    try {
        $sql = @"
SELECT 
    s.SupplierName AS [Supplier Name],
    s.ContactPerson AS [Contact Person],
    s.Phone AS [Phone],
    s.City AS [City],
    s.PaymentTerms AS [Payment Terms],
    s.CreditLimit AS [Credit Limit]
FROM Suppliers s 
WHERE s.IsActive = True
ORDER BY s.SupplierName
"@
        
        $recordset = New-Object -ComObject ADODB.Recordset
        $recordset.Open($sql, $connection)
        
        $recordCount = 0
        Write-Host "Suppliers Report:" -ForegroundColor Yellow
        while (-not $recordset.EOF) {
            $supplierName = $recordset.Fields.Item("Supplier Name").Value
            $contactPerson = $recordset.Fields.Item("Contact Person").Value
            $phone = $recordset.Fields.Item("Phone").Value
            $city = $recordset.Fields.Item("City").Value
            $paymentTerms = $recordset.Fields.Item("Payment Terms").Value
            $creditLimit = $recordset.Fields.Item("Credit Limit").Value
            
            Write-Host "  $supplierName - Contact: $contactPerson, Phone: $phone, City: $city" -ForegroundColor White
            Write-Host "    Payment Terms: $paymentTerms, Credit Limit: $creditLimit" -ForegroundColor Gray
            $recordCount++
            $recordset.MoveNext()
        }
        $recordset.Close()
        Write-Host "Total suppliers: $recordCount" -ForegroundColor Green
    }
    catch {
        Write-Host "Error in suppliers report query" -ForegroundColor Red
        Write-Host $_.Exception.Message -ForegroundColor Yellow
    }
    
    # Test 4: Products Profitability Analysis
    Write-Host "`n=== Test 4: Products Profitability Analysis ===" -ForegroundColor Cyan
    
    try {
        $sql = @"
SELECT 
    p.ProductName AS [Product Name],
    c.CategoryName AS [Category],
    p.SalePrice AS [Sale Price],
    p.ProductionCost AS [Production Cost],
    (p.SalePrice - p.ProductionCost) AS [Profit Margin],
    IIf(p.ProductionCost > 0, ((p.SalePrice - p.ProductionCost) / p.ProductionCost) * 100, 0) AS [Profit Percentage]
FROM Products p 
LEFT JOIN Categories c ON p.CategoryID = c.CategoryID
WHERE p.IsActive = True
ORDER BY [Profit Percentage] DESC
"@
        
        $recordset = New-Object -ComObject ADODB.Recordset
        $recordset.Open($sql, $connection)
        
        $recordCount = 0
        Write-Host "Products Profitability Analysis:" -ForegroundColor Yellow
        while (-not $recordset.EOF) {
            $productName = $recordset.Fields.Item("Product Name").Value
            $category = $recordset.Fields.Item("Category").Value
            $salePrice = $recordset.Fields.Item("Sale Price").Value
            $productionCost = $recordset.Fields.Item("Production Cost").Value
            $profitMargin = $recordset.Fields.Item("Profit Margin").Value
            $profitPercentage = [math]::Round($recordset.Fields.Item("Profit Percentage").Value, 2)
            
            Write-Host "  $productName ($category)" -ForegroundColor White
            Write-Host "    Sale Price: $salePrice, Cost: $productionCost, Margin: $profitMargin ($profitPercentage%)" -ForegroundColor Gray
            $recordCount++
            $recordset.MoveNext()
        }
        $recordset.Close()
        Write-Host "Total products: $recordCount" -ForegroundColor Green
    }
    catch {
        Write-Host "Error in profitability analysis query" -ForegroundColor Red
        Write-Host $_.Exception.Message -ForegroundColor Yellow
    }
    
    # Test 5: Production Stages Report
    Write-Host "`n=== Test 5: Production Stages Report ===" -ForegroundColor Cyan
    
    try {
        $sql = @"
SELECT 
    ps.StageName AS [Stage Name],
    ps.Description AS [Description],
    ps.StageOrder AS [Order],
    ps.StageCost AS [Stage Cost],
    ps.StageTime AS [Time (minutes)]
FROM ProductionStages ps
ORDER BY ps.StageOrder
"@
        
        $recordset = New-Object -ComObject ADODB.Recordset
        $recordset.Open($sql, $connection)
        
        $recordCount = 0
        $totalCost = 0
        $totalTime = 0
        Write-Host "Production Stages Report:" -ForegroundColor Yellow
        while (-not $recordset.EOF) {
            $stageName = $recordset.Fields.Item("Stage Name").Value
            $description = $recordset.Fields.Item("Description").Value
            $order = $recordset.Fields.Item("Order").Value
            $stageCost = $recordset.Fields.Item("Stage Cost").Value
            $stageTime = $recordset.Fields.Item("Time (minutes)").Value
            
            Write-Host "  $order. $stageName - Cost: $stageCost, Time: $stageTime min" -ForegroundColor White
            Write-Host "     $description" -ForegroundColor Gray
            
            $totalCost += $stageCost
            $totalTime += $stageTime
            $recordCount++
            $recordset.MoveNext()
        }
        $recordset.Close()
        Write-Host "Total stages: $recordCount, Total cost: $totalCost, Total time: $totalTime minutes" -ForegroundColor Green
    }
    catch {
        Write-Host "Error in production stages report query" -ForegroundColor Red
        Write-Host $_.Exception.Message -ForegroundColor Yellow
    }
    
    Write-Host "`n=== Query Testing Summary ===" -ForegroundColor Cyan
    Write-Host "All basic queries tested successfully" -ForegroundColor Green
    Write-Host "Database structure and relationships are working correctly" -ForegroundColor Green
    
    $connection.Close()
}
catch {
    Write-Host "Database query testing failed" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Yellow
}
