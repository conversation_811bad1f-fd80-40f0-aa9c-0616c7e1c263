# إنشاء الاستعلامات العربية في Microsoft Access
Write-Host "Creating Arabic Queries in Access Database" -ForegroundColor Green

try {
    $connectionString = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=" + (Get-Location).Path + "\Factory_Accounting_System.accdb"
    $connection = New-Object -ComObject ADODB.Connection
    $connection.Open($connectionString)
    
    Write-Host "Database connection successful" -ForegroundColor Green
    
    # 1. تقرير المخزون الشامل
    Write-Host "Creating Inventory Report Query..." -ForegroundColor Yellow
    $sql = @"
SELECT 
    rm.MaterialName AS [اسم المادة],
    c.CategoryName AS [الفئة],
    u.UnitName AS [وحدة القياس],
    IIF(inv.AvailableQuantity IS NULL, 0, inv.AvailableQuantity) AS [الكمية المتاحة],
    IIF(inv.AverageCost IS NULL, 0, inv.AverageCost) AS [متوسط التكلفة],
    IIF(inv.TotalValue IS NULL, 0, inv.TotalValue) AS [إجمالي القيمة],
    rm.MinimumLevel AS [الحد الأدنى],
    IIF(IIF(inv.AvailableQuantity IS NULL, 0, inv.AvailableQuantity) <= rm.MinimumLevel, "تحت الحد الأدنى", "مناسب") AS [حالة المخزون],
    inv.LastMovementDate AS [تاريخ آخر حركة],
    rm.AveragePrice AS [متوسط سعر الشراء],
    IIF(rm.IsActive, "نشط", "غير نشط") AS [الحالة]
FROM ((RawMaterials rm 
LEFT JOIN Categories c ON rm.CategoryID = c.CategoryID) 
LEFT JOIN Units u ON rm.UnitID = u.UnitID) 
LEFT JOIN RawMaterialsInventory inv ON rm.MaterialID = inv.MaterialID
ORDER BY c.CategoryName, rm.MaterialName
"@
    
    $connection.Execute("CREATE VIEW تقرير_المخزون_الشامل AS " + $sql)
    Write-Host "✓ Inventory Report Query created" -ForegroundColor Green
    
    # 2. تقرير تكاليف الإنتاج التفصيلي
    Write-Host "Creating Production Cost Report Query..." -ForegroundColor Yellow
    $sql = @"
SELECT 
    po.OrderID AS [رقم أمر الإنتاج],
    p.ProductName AS [اسم المنتج],
    c.CategoryName AS [فئة المنتج],
    po.RequiredQuantity AS [الكمية المطلوبة],
    po.ProducedQuantity AS [الكمية المنتجة],
    IIF(po.RequiredQuantity > 0, (po.ProducedQuantity / po.RequiredQuantity) * 100, 0) AS [نسبة الإنجاز %],
    po.EstimatedCost AS [التكلفة المقدرة],
    IIF(po.ActualCost IS NULL, 0, po.ActualCost) AS [التكلفة الفعلية],
    (IIF(po.ActualCost IS NULL, 0, po.ActualCost) - po.EstimatedCost) AS [انحراف التكلفة],
    IIF(IIF(po.ActualCost IS NULL, 0, po.ActualCost) > po.EstimatedCost, "تجاوز الميزانية", "ضمن الميزانية") AS [حالة التكلفة],
    po.OrderDate AS [تاريخ الأمر],
    po.StartDate AS [تاريخ البدء],
    po.EndDate AS [تاريخ الانتهاء],
    po.OrderStatus AS [حالة الأمر],
    po.EntryUser AS [المستخدم المسؤول]
FROM (ProductionOrders po 
LEFT JOIN Products p ON po.ProductID = p.ProductID)
LEFT JOIN Categories c ON p.CategoryID = c.CategoryID
ORDER BY po.OrderDate DESC
"@
    
    $connection.Execute("CREATE VIEW تقرير_تكاليف_الإنتاج_التفصيلي AS " + $sql)
    Write-Host "✓ Production Cost Report Query created" -ForegroundColor Green
    
    # 3. تقرير الفواتير المستحقة والمتأخرة
    Write-Host "Creating Due Invoices Report Query..." -ForegroundColor Yellow
    $sql = @"
SELECT 
    pi.InvoiceID AS [رقم الفاتورة],
    pi.SupplierInvoiceNumber AS [رقم فاتورة المورد],
    s.SupplierName AS [اسم المورد],
    s.ContactPerson AS [جهة الاتصال],
    s.Phone AS [رقم الهاتف],
    pi.InvoiceDate AS [تاريخ الفاتورة],
    pi.DueDate AS [تاريخ الاستحقاق],
    pi.TotalAmount AS [إجمالي الفاتورة],
    pi.PaidAmount AS [المبلغ المدفوع],
    pi.RemainingAmount AS [المبلغ المتبقي],
    DateDiff("d", pi.DueDate, Date()) AS [أيام التأخير],
    IIF(pi.DueDate < Date(), "متأخرة", 
        IIF(pi.DueDate = Date(), "مستحقة اليوم", "غير مستحقة")) AS [حالة الاستحقاق],
    pi.InvoiceStatus AS [حالة الفاتورة],
    cur.CurrencyName AS [العملة]
FROM (PurchaseInvoices pi 
LEFT JOIN Suppliers s ON pi.SupplierID = s.SupplierID)
LEFT JOIN Currencies cur ON pi.CurrencyID = cur.CurrencyID
WHERE pi.InvoiceStatus = 'Open' AND pi.RemainingAmount > 0
ORDER BY DateDiff("d", pi.DueDate, Date()) DESC, pi.RemainingAmount DESC
"@
    
    $connection.Execute("CREATE VIEW تقرير_الفواتير_المستحقة_والمتأخرة AS " + $sql)
    Write-Host "✓ Due Invoices Report Query created" -ForegroundColor Green
    
    # 4. تقرير تحليل الأرباح والمبيعات
    Write-Host "Creating Profitability Analysis Query..." -ForegroundColor Yellow
    $sql = @"
SELECT 
    p.ProductName AS [اسم المنتج],
    c.CategoryName AS [فئة المنتج],
    u.UnitName AS [وحدة القياس],
    p.SalePrice AS [سعر البيع],
    p.ProductionCost AS [تكلفة الإنتاج],
    (p.SalePrice - p.ProductionCost) AS [هامش الربح],
    IIF(p.ProductionCost > 0, ((p.SalePrice - p.ProductionCost) / p.ProductionCost) * 100, 0) AS [نسبة الربح %],
    IIF(inv.AvailableQuantity IS NULL, 0, inv.AvailableQuantity) AS [الكمية المتاحة],
    (IIF(inv.AvailableQuantity IS NULL, 0, inv.AvailableQuantity) * p.SalePrice) AS [قيمة المخزون بسعر البيع],
    (IIF(inv.AvailableQuantity IS NULL, 0, inv.AvailableQuantity) * p.ProductionCost) AS [قيمة المخزون بالتكلفة],
    p.MinimumLevel AS [الحد الأدنى],
    IIF(IIF(inv.AvailableQuantity IS NULL, 0, inv.AvailableQuantity) <= p.MinimumLevel, "تحت الحد الأدنى", "مناسب") AS [حالة المخزون],
    IIF(p.IsActive, "نشط", "غير نشط") AS [حالة المنتج]
FROM ((Products p 
LEFT JOIN Categories c ON p.CategoryID = c.CategoryID)
LEFT JOIN Units u ON p.UnitID = u.UnitID)
LEFT JOIN FinishedProductsInventory inv ON p.ProductID = inv.ProductID
WHERE p.IsActive = True
ORDER BY IIF(p.ProductionCost > 0, ((p.SalePrice - p.ProductionCost) / p.ProductionCost) * 100, 0) DESC
"@
    
    $connection.Execute("CREATE VIEW تقرير_تحليل_الأرباح_والمبيعات AS " + $sql)
    Write-Host "✓ Profitability Analysis Query created" -ForegroundColor Green
    
    # 5. تقرير حركات المخزون التفصيلية
    Write-Host "Creating Inventory Movements Report Query..." -ForegroundColor Yellow
    $sql = @"
SELECT 
    im.MovementDate AS [تاريخ الحركة],
    IIF(im.MovementType = 'Purchase', 'شراء',
        IIF(im.MovementType = 'Production', 'إنتاج',
            IIF(im.MovementType = 'Sale', 'بيع', im.MovementType))) AS [نوع الحركة],
    IIF(im.ItemType = 'Raw Material', 'مادة خام', 'منتج نهائي') AS [نوع المادة],
    IIF(im.ItemType = 'Raw Material', rm.MaterialName, p.ProductName) AS [اسم المادة/المنتج],
    im.Quantity AS [الكمية],
    im.UnitPrice AS [سعر الوحدة],
    im.TotalValue AS [إجمالي القيمة],
    im.BalanceBefore AS [الرصيد قبل الحركة],
    im.BalanceAfter AS [الرصيد بعد الحركة],
    IIF(im.ReferenceType = 'Purchase Invoice', 'فاتورة شراء',
        IIF(im.ReferenceType = 'Production Order', 'أمر إنتاج', im.ReferenceType)) AS [نوع المرجع],
    im.ReferenceNumber AS [رقم المرجع],
    im.Notes AS [ملاحظات],
    im.EntryUser AS [المستخدم]
FROM (InventoryMovements im 
LEFT JOIN RawMaterials rm ON im.ItemID = rm.MaterialID AND im.ItemType = 'Raw Material')
LEFT JOIN Products p ON im.ItemID = p.ProductID AND im.ItemType = 'Finished Product'
ORDER BY im.MovementDate DESC
"@
    
    $connection.Execute("CREATE VIEW تقرير_حركات_المخزون_التفصيلية AS " + $sql)
    Write-Host "✓ Inventory Movements Report Query created" -ForegroundColor Green
    
    Write-Host "`nAll Arabic queries created successfully!" -ForegroundColor Green
    Write-Host "You can now use these queries in Access forms and reports" -ForegroundColor Cyan
    
    $connection.Close()
}
catch {
    Write-Host "Error creating Arabic queries" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Yellow
}
