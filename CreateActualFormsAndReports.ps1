# Create Actual Forms and Reports in Access Database
Write-Host "Creating Forms and Reports in Factory Accounting System" -ForegroundColor Green

try {
    $connectionString = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=" + (Get-Location).Path + "\Factory_Accounting_System.accdb"
    $connection = New-Object -ComObject ADODB.Connection
    $connection.Open($connectionString)
    
    Write-Host "Database connection successful" -ForegroundColor Green
    
    # Step 1: Create Arabic Queries
    Write-Host "`nStep 1: Creating Arabic Queries..." -ForegroundColor Cyan
    
    # Query 1: Suppliers Arabic Query
    try {
        $sql = @"
SELECT 
    s.SupplierID,
    s.SupplierName AS [اسم المورد],
    s.ContactPerson AS [جهة الاتصال],
    s.Phone AS [الهاتف],
    s.Mobile AS [الجوال],
    s.Email AS [البريد الإلكتروني],
    s.City AS [المدينة],
    s.Country AS [الدولة],
    s.Address AS [العنوان],
    s.PaymentTerms AS [شروط الدفع],
    s.CreditLimit AS [حد الائتمان],
    s.AccountBalance AS [رصيد الحساب],
    IIF(s.IsActive, "نشط", "غير نشط") AS [الحالة]
FROM Suppliers s
ORDER BY s.SupplierName
"@
        $connection.Execute("CREATE VIEW SuppliersArabicView AS " + $sql)
        Write-Host "✓ Suppliers Arabic Query created" -ForegroundColor Green
    }
    catch {
        Write-Host "Suppliers query already exists or error occurred" -ForegroundColor Yellow
    }
    
    # Query 2: Raw Materials Arabic Query
    try {
        $sql = @"
SELECT 
    rm.MaterialID,
    rm.MaterialName AS [اسم المادة],
    c.CategoryName AS [الفئة],
    u.UnitName AS [وحدة القياس],
    rm.MinimumLevel AS [الحد الأدنى],
    rm.AveragePrice AS [متوسط السعر],
    rm.Notes AS [ملاحظات],
    IIF(rm.IsActive, "نشط", "غير نشط") AS [الحالة]
FROM (RawMaterials rm 
LEFT JOIN Categories c ON rm.CategoryID = c.CategoryID) 
LEFT JOIN Units u ON rm.UnitID = u.UnitID
ORDER BY c.CategoryName, rm.MaterialName
"@
        $connection.Execute("CREATE VIEW RawMaterialsArabicView AS " + $sql)
        Write-Host "✓ Raw Materials Arabic Query created" -ForegroundColor Green
    }
    catch {
        Write-Host "Raw Materials query already exists or error occurred" -ForegroundColor Yellow
    }
    
    # Query 3: Inventory Report Query
    try {
        $sql = @"
SELECT 
    rm.MaterialName AS [اسم المادة],
    c.CategoryName AS [الفئة],
    u.UnitName AS [وحدة القياس],
    IIF(inv.AvailableQuantity IS NULL, 0, inv.AvailableQuantity) AS [الكمية المتاحة],
    IIF(inv.AverageCost IS NULL, 0, inv.AverageCost) AS [متوسط التكلفة],
    IIF(inv.TotalValue IS NULL, 0, inv.TotalValue) AS [إجمالي القيمة],
    rm.MinimumLevel AS [الحد الأدنى],
    IIF(IIF(inv.AvailableQuantity IS NULL, 0, inv.AvailableQuantity) <= rm.MinimumLevel, "تحت الحد الأدنى", "مناسب") AS [حالة المخزون]
FROM ((RawMaterials rm 
LEFT JOIN Categories c ON rm.CategoryID = c.CategoryID) 
LEFT JOIN Units u ON rm.UnitID = u.UnitID) 
LEFT JOIN RawMaterialsInventory inv ON rm.MaterialID = inv.MaterialID
WHERE rm.IsActive = True
ORDER BY c.CategoryName, rm.MaterialName
"@
        $connection.Execute("CREATE VIEW InventoryReportArabicView AS " + $sql)
        Write-Host "✓ Inventory Report Query created" -ForegroundColor Green
    }
    catch {
        Write-Host "Inventory Report query already exists or error occurred" -ForegroundColor Yellow
    }
    
    # Query 4: Purchase Invoices Arabic Query
    try {
        $sql = @"
SELECT 
    pi.InvoiceID,
    pi.SupplierInvoiceNumber AS [رقم فاتورة المورد],
    s.SupplierName AS [اسم المورد],
    pi.InvoiceDate AS [تاريخ الفاتورة],
    pi.DueDate AS [تاريخ الاستحقاق],
    cur.CurrencyName AS [العملة],
    pi.SubTotal AS [إجمالي قبل الضريبة],
    pi.TaxAmount AS [قيمة الضريبة],
    pi.TotalAmount AS [إجمالي الفاتورة],
    pi.PaidAmount AS [المبلغ المدفوع],
    pi.RemainingAmount AS [المبلغ المتبقي],
    IIF(pi.InvoiceStatus = 'Open', 'مفتوحة', 
        IIF(pi.InvoiceStatus = 'Paid', 'مدفوعة', 'ملغاة')) AS [حالة الفاتورة]
FROM (PurchaseInvoices pi 
LEFT JOIN Suppliers s ON pi.SupplierID = s.SupplierID)
LEFT JOIN Currencies cur ON pi.CurrencyID = cur.CurrencyID
ORDER BY pi.InvoiceDate DESC
"@
        $connection.Execute("CREATE VIEW PurchaseInvoicesArabicView AS " + $sql)
        Write-Host "✓ Purchase Invoices Arabic Query created" -ForegroundColor Green
    }
    catch {
        Write-Host "Purchase Invoices query already exists or error occurred" -ForegroundColor Yellow
    }
    
    # Query 5: Production Cost Report Query
    try {
        $sql = @"
SELECT 
    po.OrderID AS [رقم أمر الإنتاج],
    p.ProductName AS [اسم المنتج],
    c.CategoryName AS [فئة المنتج],
    po.RequiredQuantity AS [الكمية المطلوبة],
    po.ProducedQuantity AS [الكمية المنتجة],
    IIF(po.RequiredQuantity > 0, (po.ProducedQuantity / po.RequiredQuantity) * 100, 0) AS [نسبة الإنجاز %],
    po.EstimatedCost AS [التكلفة المقدرة],
    IIF(po.ActualCost IS NULL, 0, po.ActualCost) AS [التكلفة الفعلية],
    po.OrderDate AS [تاريخ الأمر],
    IIF(po.OrderStatus = 'New', 'جديد',
        IIF(po.OrderStatus = 'In Progress', 'قيد التنفيذ',
            IIF(po.OrderStatus = 'Complete', 'مكتمل', 'ملغي'))) AS [حالة الأمر]
FROM (ProductionOrders po 
LEFT JOIN Products p ON po.ProductID = p.ProductID)
LEFT JOIN Categories c ON p.CategoryID = c.CategoryID
ORDER BY po.OrderDate DESC
"@
        $connection.Execute("CREATE VIEW ProductionCostReportArabicView AS " + $sql)
        Write-Host "✓ Production Cost Report Query created" -ForegroundColor Green
    }
    catch {
        Write-Host "Production Cost Report query already exists or error occurred" -ForegroundColor Yellow
    }
    
    Write-Host "`nStep 2: Testing queries..." -ForegroundColor Cyan
    
    # Test the queries
    $queries = @("SuppliersArabicView", "RawMaterialsArabicView", "InventoryReportArabicView", "PurchaseInvoicesArabicView", "ProductionCostReportArabicView")
    
    foreach ($query in $queries) {
        try {
            $recordset = New-Object -ComObject ADODB.Recordset
            $recordset.Open("SELECT COUNT(*) as RecordCount FROM [$query]", $connection)
            $count = $recordset.Fields.Item("RecordCount").Value
            $recordset.Close()
            Write-Host "✓ $query : $count records" -ForegroundColor Green
        }
        catch {
            Write-Host "✗ $query : Error" -ForegroundColor Red
        }
    }
    
    Write-Host "`n=== Arabic Queries Created Successfully! ===" -ForegroundColor Cyan
    Write-Host "✓ SuppliersArabicView - for Suppliers forms and reports" -ForegroundColor Green
    Write-Host "✓ RawMaterialsArabicView - for Raw Materials forms and reports" -ForegroundColor Green
    Write-Host "✓ InventoryReportArabicView - for Inventory reports" -ForegroundColor Green
    Write-Host "✓ PurchaseInvoicesArabicView - for Purchase Invoice forms and reports" -ForegroundColor Green
    Write-Host "✓ ProductionCostReportArabicView - for Production Cost reports" -ForegroundColor Green
    
    Write-Host "`nNext Steps:" -ForegroundColor Yellow
    Write-Host "1. Open Access and refresh Navigation Pane (F5)" -ForegroundColor White
    Write-Host "2. You will find new queries in the Queries section" -ForegroundColor White
    Write-Host "3. Use Form Wizard with these queries to create Arabic forms" -ForegroundColor White
    Write-Host "4. Use Report Wizard with these queries to create Arabic reports" -ForegroundColor White
    Write-Host "5. All field names will appear in Arabic automatically" -ForegroundColor White
    
    Write-Host "`nQueries are ready for:" -ForegroundColor Yellow
    Write-Host "- Creating forms with Arabic field names" -ForegroundColor White
    Write-Host "- Creating reports with Arabic column headers" -ForegroundColor White
    Write-Host "- Building navigation forms" -ForegroundColor White
    Write-Host "- Data entry and reporting in Arabic" -ForegroundColor White
    
    $connection.Close()
    Write-Host "`nDatabase updated successfully!" -ForegroundColor Green
}
catch {
    Write-Host "Error creating queries: $($_.Exception.Message)" -ForegroundColor Red
}
