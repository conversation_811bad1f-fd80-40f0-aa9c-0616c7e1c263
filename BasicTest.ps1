# Basic Database Test
Write-Host "Testing Database Tables" -ForegroundColor Green

try {
    $connectionString = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=" + (Get-Location).Path + "\Factory_Accounting_System.accdb"
    $connection = New-Object -ComObject ADODB.Connection
    $connection.Open($connectionString)
    
    Write-Host "Database connection successful" -ForegroundColor Green
    
    $expectedTables = @("Units", "Currencies", "Categories", "RawMaterials", "Suppliers")
    
    $existingTables = 0
    $missingTables = 0
    
    foreach ($tableName in $expectedTables) {
        try {
            $recordset = New-Object -ComObject ADODB.Recordset
            $recordset.Open("SELECT TOP 1 * FROM [$tableName]", $connection)
            $fieldCount = $recordset.Fields.Count
            $recordset.Close()
            
            Write-Host "Table $tableName exists with $fieldCount fields" -ForegroundColor Green
            $existingTables++
        }
        catch {
            Write-Host "Table $tableName is missing" -ForegroundColor Red
            $missingTables++
        }
    }
    
    Write-Host "Existing tables: $existingTables" -ForegroundColor Green
    Write-Host "Missing tables: $missingTables" -ForegroundColor Red
    
    $connection.Close()
}
catch {
    Write-Host "Database test failed" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Yellow
}
