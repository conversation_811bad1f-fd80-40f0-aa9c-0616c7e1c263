# ملخص مشروع نظام محاسبة مصنع المعمول والمخللات

## 🎯 نظرة عامة على المشروع

تم إنشاء نظام محاسبي شامل ومتكامل لمصنع متخصص في تصنيع المعمول والمخللات باستخدام Microsoft Access مع واجهة باللغة العربية. النظام يوفر إدارة كاملة للمشتريات والتصنيع والمخزون مع حسابات تلقائية للتكاليف والأرباح.

## 📁 الملفات المُنشأة

### 1. قاعدة البيانات الأساسية
- **Factory_Accounting_System.accdb** - ملف قاعدة البيانات الرئيسي
- **نظام_محاسبة_المصنع.sql** - سكريبت إنشاء الجداول الأساسية

### 2. ملفات الإعداد والتشغيل
- **CreateDB.ps1** - سكريبت PowerShell لإنشاء قاعدة البيانات
- **CreateTables.ps1** - سكريبت إنشاء الجداول
- **InsertBasicData.ps1** - سكريبت إدراج البيانات الأساسية

### 3. ملفات البرمجة والتطوير
- **إنشاء_النماذج.txt** - كود VBA لإنشاء النماذج الأساسية
- **الحسابات_التلقائية.txt** - كود VBA للحسابات التلقائية وتحديث المخزون
- **الاستعلامات_والحسابات.sql** - استعلامات التقارير والتحليلات

### 4. البيانات والتوثيق
- **بيانات_تجريبية.sql** - بيانات تجريبية شاملة للاختبار
- **دليل_الاستخدام.md** - دليل شامل لاستخدام النظام
- **ملخص_المشروع.md** - هذا الملف

## 🗄️ هيكل قاعدة البيانات

### الجداول الأساسية (17 جدول):

#### 📦 إدارة المشتريات (7 جداول)
1. **Units** - وحدات القياس
2. **Currencies** - العملات
3. **Categories** - فئات المواد والمنتجات
4. **RawMaterials** - المواد الخام
5. **Suppliers** - الموردين
6. **PurchaseInvoices** - فواتير الشراء
7. **PurchaseInvoiceDetails** - تفاصيل فواتير الشراء

#### 🏭 إدارة التصنيع (6 جداول)
8. **Products** - المنتجات النهائية
9. **ProductRecipes** - وصفات الإنتاج
10. **ProductionStages** - مراحل التصنيع
11. **ProductionOrders** - أوامر الإنتاج
12. **CostCenters** - مراكز التكلفة
13. **ProductionCosts** - تكاليف الإنتاج

#### 📊 إدارة المخزون (4 جداول)
14. **RawMaterialsInventory** - مخزون المواد الخام
15. **FinishedProductsInventory** - مخزون المنتجات النهائية
16. **InventoryMovements** - حركات المخزون
17. **ProductionOrderDetails** - تفاصيل أوامر الإنتاج

## 📈 الاستعلامات والتقارير (8 تقارير)

1. **تقرير_المخزون_الشامل** - حالة المخزون الكاملة
2. **تقرير_تكاليف_الإنتاج_التفصيلي** - تحليل تكاليف الإنتاج
3. **الفواتير_المستحقة_والمتأخرة** - متابعة المدفوعات
4. **تحليل_الأرباح_والمبيعات** - تحليل الربحية
5. **حركات_المخزون_التفصيلية** - تتبع حركات المخزون
6. **تقرير_الموردين_والأرصدة** - حالة الموردين
7. **تقرير_استهلاك_المواد_الخام** - تحليل الاستهلاك
8. **تقرير_كفاءة_الإنتاج** - قياس الأداء

## ⚙️ الوظائف التلقائية

### 🔄 تحديث المخزون التلقائي
- تحديث مخزون المواد الخام عند الشراء
- تحديث مخزون المواد الخام عند الاستهلاك في الإنتاج
- إضافة المنتجات النهائية للمخزون عند اكتمال الإنتاج
- تسجيل جميع حركات المخزون تلقائياً

### 💰 الحسابات المالية التلقائية
- حساب متوسط تكلفة المواد الخام
- حساب تكاليف الإنتاج الفعلية
- تحديث أرصدة الموردين
- حساب الأرباح والخسائر لكل منتج

### 🚨 التنبيهات والتحذيرات
- تنبيهات المخزون تحت الحد الأدنى
- تحذيرات الفواتير المتأخرة
- تنبيهات تجاوز حد الائتمان

## 🎨 الواجهة العربية

- جميع أسماء الحقول والجداول باللغة العربية
- النماذج والتقارير مصممة للعرض من اليمين لليسار
- دعم كامل للنصوص العربية
- واجهة مستخدم سهلة ومألوفة

## 📊 البيانات التجريبية المُدرجة

### المواد الخام (23 مادة):
- **دقيق ونشويات**: دقيق أبيض، دقيق أسمر، سميد
- **سكر ومحليات**: سكر أبيض، سكر بني، عسل طبيعي
- **زيوت ودهون**: زيت زيتون، زيت نباتي، زبدة
- **توابل وبهارات**: قرفة، هيل، ماء ورد، ملح
- **خضروات وفواكه**: تمر، جوز، فستق، خيار، جزر
- **مواد حافظة**: خل، مواد حافظة طبيعية
- **عبوات وتغليف**: علب زجاجية، أكياس تغليف

### المنتجات النهائية (8 منتجات):
- **معمول**: بالتمر (صغير وكبير)، بالجوز، بالفستق، مشكل
- **مخللات**: خيار، جزر، مشكل

### الموردين (6 موردين):
- مؤسسة الدقيق الذهبي
- شركة السكر والمحليات
- مصنع الزيوت الطبيعية
- تجارة التوابل والبهارات
- مزارع الخضار الطازجة
- مصنع العبوات والتغليف

## 🚀 خطوات التشغيل

### 1. الإعداد الأولي
```powershell
# تشغيل سكريبت إنشاء قاعدة البيانات
powershell -ExecutionPolicy Bypass -File "CreateDB.ps1"

# تشغيل سكريبت إنشاء الجداول
powershell -ExecutionPolicy Bypass -File "CreateTables.ps1"

# إدراج البيانات الأساسية
powershell -ExecutionPolicy Bypass -File "InsertBasicData.ps1"
```

### 2. إضافة الكود البرمجي
- نسخ كود VBA من ملف "إنشاء_النماذج.txt"
- نسخ كود VBA من ملف "الحسابات_التلقائية.txt"
- تشغيل الاستعلامات من ملف "الاستعلامات_والحسابات.sql"

### 3. إدراج البيانات التجريبية
- تشغيل سكريبت "بيانات_تجريبية.sql"

## 🔧 المتطلبات التقنية

- **Microsoft Access 2016** أو أحدث
- **Windows 10** أو أحدث
- **PowerShell 5.0** أو أحدث
- **ذاكرة**: 4 جيجابايت RAM كحد أدنى
- **مساحة القرص**: 500 ميجابايت للنظام والبيانات

## 📋 الميزات الرئيسية

✅ **إدارة شاملة للمشتريات** مع تتبع الموردين والفواتير  
✅ **نظام تصنيع متكامل** مع تتبع مراحل الإنتاج  
✅ **إدارة مخزون ذكية** مع تحديث تلقائي  
✅ **حسابات مالية دقيقة** للتكاليف والأرباح  
✅ **تقارير تحليلية شاملة** لاتخاذ القرارات  
✅ **واجهة عربية كاملة** سهلة الاستخدام  
✅ **تنبيهات ذكية** للمخزون والمدفوعات  
✅ **نظام أمان** لحماية البيانات  

## 🎯 الفوائد المحققة

1. **توفير الوقت**: أتمتة العمليات المحاسبية والمخزنية
2. **دقة البيانات**: تقليل الأخطاء البشرية في الحسابات
3. **اتخاذ قرارات مدروسة**: تقارير تحليلية شاملة
4. **تحسين الربحية**: تتبع دقيق للتكاليف والأرباح
5. **إدارة أفضل للمخزون**: تجنب النقص أو الفائض
6. **متابعة الموردين**: تحسين العلاقات التجارية

## 📞 الدعم والتطوير

النظام قابل للتطوير والتخصيص حسب احتياجات كل مصنع. يمكن إضافة ميزات جديدة مثل:
- نظام نقاط البيع (POS)
- تكامل مع أنظمة المحاسبة الخارجية
- تطبيق جوال للمتابعة
- تقارير متقدمة بالذكاء الاصطناعي

---

**تم إنجاز المشروع بنجاح وهو جاهز للاستخدام الفوري! 🎉**
