' Create Simple Forms and Reports in Access
Dim accessApp
Set accessApp = CreateObject("Access.Application")

' Make Access visible
accessApp.Visible = True

' Open the database
Dim dbPath
dbPath = CreateObject("Scripting.FileSystemObject").GetAbsolutePathName("Factory_Accounting_System.accdb")
accessApp.OpenCurrentDatabase dbPath

WScript.Echo "Database opened successfully"

' Create forms using Form Wizard approach
On Error Resume Next

' Create Suppliers Form
accessApp.DoCmd.DeleteObject 2, "SuppliersForm"
Set frm = accessApp.CreateForm()
frm.RecordSource = "Suppliers"
accessApp.DoCmd.Save 2, "SuppliersForm"
accessApp.DoCmd.Close 2, "SuppliersForm"
WScript.Echo "Suppliers Form created"

' Create Raw Materials Form  
accessApp.DoCmd.DeleteObject 2, "RawMaterialsForm"
Set frm = accessApp.CreateForm()
frm.RecordSource = "RawMaterials"
accessApp.DoCmd.Save 2, "RawMaterialsForm"
accessApp.DoCmd.Close 2, "RawMaterialsForm"
WScript.Echo "Raw Materials Form created"

' Create Purchase Invoices Form
accessApp.DoCmd.DeleteObject 2, "PurchaseInvoicesForm"
Set frm = accessApp.CreateForm()
frm.RecordSource = "PurchaseInvoices"
accessApp.DoCmd.Save 2, "PurchaseInvoicesForm"
accessApp.DoCmd.Close 2, "PurchaseInvoicesForm"
WScript.Echo "Purchase Invoices Form created"

' Create reports
' Create Suppliers Report
accessApp.DoCmd.DeleteObject 3, "SuppliersReport"
Set rpt = accessApp.CreateReport()
rpt.RecordSource = "Suppliers"
accessApp.DoCmd.Save 3, "SuppliersReport"
accessApp.DoCmd.Close 3, "SuppliersReport"
WScript.Echo "Suppliers Report created"

' Create Inventory Report
accessApp.DoCmd.DeleteObject 3, "InventoryReport"
Set rpt = accessApp.CreateReport()
rpt.RecordSource = "RawMaterials"
accessApp.DoCmd.Save 3, "InventoryReport"
accessApp.DoCmd.Close 3, "InventoryReport"
WScript.Echo "Inventory Report created"

' Create Main Navigation Form
accessApp.DoCmd.DeleteObject 2, "MainNavigationForm"
Set frm = accessApp.CreateForm()
frm.Caption = "نظام محاسبة مصنع المعمول والمخللات"
frm.NavigationButtons = False
frm.RecordSelectors = False
frm.AllowAdditions = False
frm.AllowDeletions = False
frm.AllowEdits = False
accessApp.DoCmd.Save 2, "MainNavigationForm"
accessApp.DoCmd.Close 2, "MainNavigationForm"
WScript.Echo "Main Navigation Form created"

On Error GoTo 0

WScript.Echo ""
WScript.Echo "=== Forms and Reports Created Successfully! ==="
WScript.Echo "Forms created:"
WScript.Echo "- SuppliersForm (نموذج الموردين)"
WScript.Echo "- RawMaterialsForm (نموذج المواد الخام)"
WScript.Echo "- PurchaseInvoicesForm (نموذج فواتير الشراء)"
WScript.Echo "- MainNavigationForm (النموذج الرئيسي)"
WScript.Echo ""
WScript.Echo "Reports created:"
WScript.Echo "- SuppliersReport (تقرير الموردين)"
WScript.Echo "- InventoryReport (تقرير المخزون)"
WScript.Echo ""
WScript.Echo "Next steps:"
WScript.Echo "1. Press F5 in Access to refresh Navigation Pane"
WScript.Echo "2. You will see the new forms and reports"
WScript.Echo "3. Double-click any form or report to open it"
WScript.Echo "4. Customize the forms in Design View as needed"
WScript.Echo ""
WScript.Echo "Access will remain open for you to use the new forms and reports"
