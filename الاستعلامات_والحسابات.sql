-- استعلامات وحسابات تلقائية لنظام محاسبة المصنع
-- ===============================================

-- 1. استعلام تقرير المخزون الشامل
CREATE VIEW تقرير_المخزون_الشامل AS
SELECT 
    rm.MaterialName AS [اسم المادة],
    c.CategoryName AS [الفئة],
    u.UnitName AS [الوحدة],
    inv.AvailableQuantity AS [الكمية المتاحة],
    inv.AverageCost AS [متوسط التكلفة],
    inv.TotalValue AS [إجمالي القيمة],
    inv.MinimumLevel AS [الحد الأدنى],
    IIf(inv.AvailableQuantity <= inv.MinimumLevel, "نعم", "لا") AS [تحت الحد الأدنى],
    inv.LastMovementDate AS [تاريخ آخر حركة]
FROM ((RawMaterials rm 
LEFT JOIN Categories c ON rm.CategoryID = c.CategoryID) 
LEFT JOIN Units u ON rm.UnitID = u.UnitID) 
LEFT JOIN RawMaterialsInventory inv ON rm.MaterialID = inv.MaterialID
WHERE rm.IsActive = True
ORDER BY c.CategoryName, rm.MaterialName;

-- 2. استعلام تكاليف الإنتاج التفصيلية
CREATE VIEW تقرير_تكاليف_الإنتاج_التفصيلي AS
SELECT 
    po.OrderID AS [رقم الأمر],
    p.ProductName AS [اسم المنتج],
    po.RequiredQuantity AS [الكمية المطلوبة],
    po.ProducedQuantity AS [الكمية المنتجة],
    po.EstimatedCost AS [التكلفة المقدرة],
    po.ActualCost AS [التكلفة الفعلية],
    (po.ActualCost - po.EstimatedCost) AS [الفرق في التكلفة],
    IIf(po.ActualCost > po.EstimatedCost, "تجاوز", "ضمن الحد") AS [حالة التكلفة],
    po.OrderStatus AS [حالة الأمر],
    po.OrderDate AS [تاريخ الأمر],
    po.StartDate AS [تاريخ البدء],
    po.EndDate AS [تاريخ الانتهاء],
    IIf(po.EndDate IS NULL, DateDiff("d", po.StartDate, Date()), DateDiff("d", po.StartDate, po.EndDate)) AS [مدة الإنتاج بالأيام]
FROM ProductionOrders po 
LEFT JOIN Products p ON po.ProductID = p.ProductID
ORDER BY po.OrderDate DESC;

-- 3. استعلام الفواتير المستحقة والمتأخرة
CREATE VIEW الفواتير_المستحقة_والمتأخرة AS
SELECT 
    pi.InvoiceID AS [رقم الفاتورة],
    s.SupplierName AS [اسم المورد],
    pi.SupplierInvoiceNumber AS [رقم فاتورة المورد],
    pi.InvoiceDate AS [تاريخ الفاتورة],
    pi.DueDate AS [تاريخ الاستحقاق],
    pi.TotalAmount AS [إجمالي الفاتورة],
    pi.PaidAmount AS [المبلغ المدفوع],
    pi.RemainingAmount AS [المبلغ المتبقي],
    DateDiff("d", pi.DueDate, Date()) AS [أيام التأخير],
    IIf(pi.DueDate < Date(), "متأخرة", IIf(pi.DueDate = Date(), "مستحقة اليوم", "غير مستحقة")) AS [حالة الاستحقاق],
    pi.InvoiceStatus AS [حالة الفاتورة]
FROM PurchaseInvoices pi 
LEFT JOIN Suppliers s ON pi.SupplierID = s.SupplierID
WHERE pi.InvoiceStatus = 'Open' AND pi.RemainingAmount > 0
ORDER BY pi.DueDate;

-- 4. استعلام تحليل المبيعات والأرباح (للمنتجات النهائية)
CREATE VIEW تحليل_الأرباح_والمبيعات AS
SELECT 
    p.ProductName AS [اسم المنتج],
    c.CategoryName AS [الفئة],
    p.SalePrice AS [سعر البيع],
    p.ProductionCost AS [تكلفة الإنتاج],
    (p.SalePrice - p.ProductionCost) AS [هامش الربح],
    IIf(p.ProductionCost > 0, ((p.SalePrice - p.ProductionCost) / p.ProductionCost) * 100, 0) AS [نسبة الربح %],
    inv.AvailableQuantity AS [الكمية المتاحة],
    (inv.AvailableQuantity * p.SalePrice) AS [قيمة المخزون بسعر البيع],
    (inv.AvailableQuantity * p.ProductionCost) AS [قيمة المخزون بالتكلفة],
    p.MinimumLevel AS [الحد الأدنى],
    IIf(inv.AvailableQuantity <= p.MinimumLevel, "نعم", "لا") AS [تحت الحد الأدنى]
FROM ((Products p 
LEFT JOIN Categories c ON p.CategoryID = c.CategoryID)
LEFT JOIN FinishedProductsInventory inv ON p.ProductID = inv.ProductID)
WHERE p.IsActive = True
ORDER BY [نسبة الربح %] DESC;

-- 5. استعلام حركات المخزون التفصيلية
CREATE VIEW حركات_المخزون_التفصيلية AS
SELECT 
    im.MovementDate AS [تاريخ الحركة],
    im.MovementType AS [نوع الحركة],
    im.ItemType AS [نوع المادة],
    IIf(im.ItemType = "مادة خام", rm.MaterialName, p.ProductName) AS [اسم المادة/المنتج],
    im.Quantity AS [الكمية],
    im.UnitPrice AS [سعر الوحدة],
    im.TotalValue AS [إجمالي القيمة],
    im.BalanceBefore AS [الرصيد قبل الحركة],
    im.BalanceAfter AS [الرصيد بعد الحركة],
    im.ReferenceType AS [نوع المرجع],
    im.ReferenceNumber AS [رقم المرجع],
    im.Notes AS [ملاحظات],
    im.EntryUser AS [المستخدم]
FROM (InventoryMovements im 
LEFT JOIN RawMaterials rm ON im.ItemID = rm.MaterialID AND im.ItemType = "مادة خام")
LEFT JOIN Products p ON im.ItemID = p.ProductID AND im.ItemType = "منتج نهائي"
ORDER BY im.MovementDate DESC, im.MovementID DESC;

-- 6. استعلام تقرير الموردين وأرصدتهم
CREATE VIEW تقرير_الموردين_والأرصدة AS
SELECT 
    s.SupplierName AS [اسم المورد],
    s.ContactPerson AS [جهة الاتصال],
    s.Phone AS [الهاتف],
    s.Email AS [البريد الإلكتروني],
    s.City AS [المدينة],
    s.PaymentTerms AS [شروط الدفع],
    s.CreditLimit AS [حد الائتمان],
    s.AccountBalance AS [رصيد الحساب],
    (s.CreditLimit - s.AccountBalance) AS [الائتمان المتاح],
    IIf(s.AccountBalance > s.CreditLimit, "تجاوز الحد", "ضمن الحد") AS [حالة الائتمان],
    COUNT(pi.InvoiceID) AS [عدد الفواتير],
    SUM(pi.TotalAmount) AS [إجمالي المشتريات],
    SUM(pi.RemainingAmount) AS [إجمالي المستحق],
    s.RegistrationDate AS [تاريخ التسجيل],
    IIf(s.IsActive, "نشط", "غير نشط") AS [الحالة]
FROM Suppliers s 
LEFT JOIN PurchaseInvoices pi ON s.SupplierID = pi.SupplierID
GROUP BY s.SupplierID, s.SupplierName, s.ContactPerson, s.Phone, s.Email, s.City, 
         s.PaymentTerms, s.CreditLimit, s.AccountBalance, s.RegistrationDate, s.IsActive
ORDER BY s.SupplierName;

-- 7. استعلام تقرير استهلاك المواد الخام
CREATE VIEW تقرير_استهلاك_المواد_الخام AS
SELECT 
    rm.MaterialName AS [اسم المادة],
    c.CategoryName AS [الفئة],
    u.UnitName AS [الوحدة],
    SUM(pod.RequiredQuantity) AS [إجمالي الكمية المطلوبة],
    SUM(pod.ConsumedQuantity) AS [إجمالي الكمية المستهلكة],
    AVG(pod.UnitCost) AS [متوسط تكلفة الوحدة],
    SUM(pod.TotalCost) AS [إجمالي التكلفة],
    COUNT(DISTINCT po.OrderID) AS [عدد أوامر الإنتاج],
    MIN(po.OrderDate) AS [أول استخدام],
    MAX(po.OrderDate) AS [آخر استخدام]
FROM (((ProductionOrderDetails pod 
LEFT JOIN ProductionOrders po ON pod.OrderID = po.OrderID)
LEFT JOIN RawMaterials rm ON pod.MaterialID = rm.MaterialID)
LEFT JOIN Categories c ON rm.CategoryID = c.CategoryID)
LEFT JOIN Units u ON rm.UnitID = u.UnitID
GROUP BY rm.MaterialID, rm.MaterialName, c.CategoryName, u.UnitName
HAVING SUM(pod.ConsumedQuantity) > 0
ORDER BY [إجمالي التكلفة] DESC;

-- 8. استعلام تقرير كفاءة الإنتاج
CREATE VIEW تقرير_كفاءة_الإنتاج AS
SELECT 
    p.ProductName AS [اسم المنتج],
    COUNT(po.OrderID) AS [عدد أوامر الإنتاج],
    SUM(po.RequiredQuantity) AS [إجمالي الكمية المطلوبة],
    SUM(po.ProducedQuantity) AS [إجمالي الكمية المنتجة],
    IIf(SUM(po.RequiredQuantity) > 0, (SUM(po.ProducedQuantity) / SUM(po.RequiredQuantity)) * 100, 0) AS [نسبة الإنجاز %],
    AVG(po.EstimatedCost) AS [متوسط التكلفة المقدرة],
    AVG(po.ActualCost) AS [متوسط التكلفة الفعلية],
    IIf(AVG(po.EstimatedCost) > 0, ((AVG(po.ActualCost) - AVG(po.EstimatedCost)) / AVG(po.EstimatedCost)) * 100, 0) AS [انحراف التكلفة %],
    AVG(DateDiff("d", po.StartDate, po.EndDate)) AS [متوسط مدة الإنتاج بالأيام],
    COUNT(IIf(po.OrderStatus = "مكتمل", 1, NULL)) AS [الأوامر المكتملة],
    COUNT(IIf(po.OrderStatus = "قيد التنفيذ", 1, NULL)) AS [الأوامر قيد التنفيذ],
    COUNT(IIf(po.OrderStatus = "متأخر", 1, NULL)) AS [الأوامر المتأخرة]
FROM Products p 
LEFT JOIN ProductionOrders po ON p.ProductID = po.ProductID
WHERE p.IsActive = True
GROUP BY p.ProductID, p.ProductName
HAVING COUNT(po.OrderID) > 0
ORDER BY [نسبة الإنجاز %] DESC;

-- ===============================================
-- إجراءات تحديث المخزون التلقائي
-- ===============================================

-- إجراء تحديث مخزون المواد الخام عند الشراء
CREATE PROCEDURE تحديث_مخزون_المواد_الخام_شراء(
    @MaterialID LONG,
    @Quantity DOUBLE,
    @UnitPrice CURRENCY,
    @InvoiceID LONG
)
AS
BEGIN
    DECLARE @CurrentQuantity DOUBLE
    DECLARE @CurrentValue CURRENCY
    DECLARE @NewQuantity DOUBLE
    DECLARE @NewAverageCost CURRENCY
    DECLARE @NewTotalValue CURRENCY

    -- الحصول على الكميات والقيم الحالية
    SELECT @CurrentQuantity = AvailableQuantity, @CurrentValue = TotalValue
    FROM RawMaterialsInventory
    WHERE MaterialID = @MaterialID

    -- حساب القيم الجديدة
    SET @NewQuantity = @CurrentQuantity + @Quantity
    SET @NewTotalValue = @CurrentValue + (@Quantity * @UnitPrice)
    SET @NewAverageCost = @NewTotalValue / @NewQuantity

    -- تحديث المخزون
    UPDATE RawMaterialsInventory
    SET AvailableQuantity = @NewQuantity,
        AverageCost = @NewAverageCost,
        TotalValue = @NewTotalValue,
        LastMovementDate = Now()
    WHERE MaterialID = @MaterialID

    -- إدراج حركة المخزون
    INSERT INTO InventoryMovements (
        MovementType, ItemType, ItemID, Quantity, UnitPrice, TotalValue,
        MovementDate, ReferenceNumber, ReferenceType, BalanceBefore, BalanceAfter,
        EntryUser
    ) VALUES (
        'دخول', 'مادة خام', @MaterialID, @Quantity, @UnitPrice, (@Quantity * @UnitPrice),
        Now(), @InvoiceID, 'فاتورة شراء', @CurrentQuantity, @NewQuantity,
        CurrentUser()
    )
END

-- إجراء تحديث مخزون المواد الخام عند الاستهلاك في الإنتاج
CREATE PROCEDURE تحديث_مخزون_المواد_الخام_استهلاك(
    @MaterialID LONG,
    @ConsumedQuantity DOUBLE,
    @ProductionOrderID LONG
)
AS
BEGIN
    DECLARE @CurrentQuantity DOUBLE
    DECLARE @CurrentAverageCost CURRENCY
    DECLARE @NewQuantity DOUBLE
    DECLARE @NewTotalValue CURRENCY

    -- الحصول على الكميات والقيم الحالية
    SELECT @CurrentQuantity = AvailableQuantity, @CurrentAverageCost = AverageCost
    FROM RawMaterialsInventory
    WHERE MaterialID = @MaterialID

    -- التحقق من توفر الكمية
    IF @CurrentQuantity < @ConsumedQuantity
    BEGIN
        RAISERROR('الكمية المطلوبة غير متوفرة في المخزون', 16, 1)
        RETURN
    END

    -- حساب القيم الجديدة
    SET @NewQuantity = @CurrentQuantity - @ConsumedQuantity
    SET @NewTotalValue = @NewQuantity * @CurrentAverageCost

    -- تحديث المخزون
    UPDATE RawMaterialsInventory
    SET AvailableQuantity = @NewQuantity,
        TotalValue = @NewTotalValue,
        LastMovementDate = Now()
    WHERE MaterialID = @MaterialID

    -- إدراج حركة المخزون
    INSERT INTO InventoryMovements (
        MovementType, ItemType, ItemID, Quantity, UnitPrice, TotalValue,
        MovementDate, ReferenceNumber, ReferenceType, BalanceBefore, BalanceAfter,
        EntryUser
    ) VALUES (
        'خروج', 'مادة خام', @MaterialID, @ConsumedQuantity, @CurrentAverageCost,
        (@ConsumedQuantity * @CurrentAverageCost), Now(), @ProductionOrderID,
        'أمر إنتاج', @CurrentQuantity, @NewQuantity, CurrentUser()
    )
END

-- إجراء إضافة منتج نهائي للمخزون
CREATE PROCEDURE إضافة_منتج_نهائي_للمخزون(
    @ProductID LONG,
    @ProducedQuantity DOUBLE,
    @ProductionCost CURRENCY,
    @ProductionOrderID LONG
)
AS
BEGIN
    DECLARE @CurrentQuantity DOUBLE
    DECLARE @CurrentValue CURRENCY
    DECLARE @NewQuantity DOUBLE
    DECLARE @NewAverageCost CURRENCY
    DECLARE @NewTotalValue CURRENCY

    -- الحصول على الكميات والقيم الحالية
    SELECT @CurrentQuantity = ISNULL(AvailableQuantity, 0),
           @CurrentValue = ISNULL(TotalValue, 0)
    FROM FinishedProductsInventory
    WHERE ProductID = @ProductID

    -- حساب القيم الجديدة
    SET @NewQuantity = @CurrentQuantity + @ProducedQuantity
    SET @NewTotalValue = @CurrentValue + (@ProducedQuantity * @ProductionCost)
    SET @NewAverageCost = @NewTotalValue / @NewQuantity

    -- تحديث أو إدراج المخزون
    IF EXISTS(SELECT 1 FROM FinishedProductsInventory WHERE ProductID = @ProductID)
    BEGIN
        UPDATE FinishedProductsInventory
        SET AvailableQuantity = @NewQuantity,
            AverageCost = @NewAverageCost,
            TotalValue = @NewTotalValue,
            LastMovementDate = Now()
        WHERE ProductID = @ProductID
    END
    ELSE
    BEGIN
        INSERT INTO FinishedProductsInventory (
            ProductID, AvailableQuantity, AverageCost, TotalValue, LastMovementDate
        ) VALUES (
            @ProductID, @ProducedQuantity, @ProductionCost,
            (@ProducedQuantity * @ProductionCost), Now()
        )
    END

    -- إدراج حركة المخزون
    INSERT INTO InventoryMovements (
        MovementType, ItemType, ItemID, Quantity, UnitPrice, TotalValue,
        MovementDate, ReferenceNumber, ReferenceType, BalanceBefore, BalanceAfter,
        EntryUser
    ) VALUES (
        'دخول', 'منتج نهائي', @ProductID, @ProducedQuantity, @ProductionCost,
        (@ProducedQuantity * @ProductionCost), Now(), @ProductionOrderID,
        'أمر إنتاج', @CurrentQuantity, @NewQuantity, CurrentUser()
    )
END

-- إجراء حساب تكلفة الإنتاج التلقائية
CREATE PROCEDURE حساب_تكلفة_الإنتاج(
    @ProductionOrderID LONG
)
AS
BEGIN
    DECLARE @TotalMaterialCost CURRENCY
    DECLARE @TotalLaborCost CURRENCY
    DECLARE @TotalOverheadCost CURRENCY
    DECLARE @TotalCost CURRENCY

    -- حساب تكلفة المواد الخام
    SELECT @TotalMaterialCost = SUM(pod.ConsumedQuantity * pod.UnitCost)
    FROM ProductionOrderDetails pod
    WHERE pod.OrderID = @ProductionOrderID

    -- حساب تكاليف العمالة والتكاليف غير المباشرة
    SELECT @TotalLaborCost = SUM(IIF(pc.CostType = 'عمالة', pc.CostAmount, 0)),
           @TotalOverheadCost = SUM(IIF(pc.CostType = 'تكاليف غير مباشرة', pc.CostAmount, 0))
    FROM ProductionCosts pc
    WHERE pc.OrderID = @ProductionOrderID

    -- حساب إجمالي التكلفة
    SET @TotalCost = ISNULL(@TotalMaterialCost, 0) + ISNULL(@TotalLaborCost, 0) + ISNULL(@TotalOverheadCost, 0)

    -- تحديث أمر الإنتاج
    UPDATE ProductionOrders
    SET ActualCost = @TotalCost
    WHERE OrderID = @ProductionOrderID
END
