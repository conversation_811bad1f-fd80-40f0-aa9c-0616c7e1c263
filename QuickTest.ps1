# Quick Test for Database Readiness
Write-Host "Testing Database for Forms and Reports" -ForegroundColor Green

try {
    $dbPath = (Get-Location).Path + "\Factory_Accounting_System.accdb"
    $connectionString = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=$dbPath"
    $connection = New-Object -ComObject ADODB.Connection
    $connection.Open($connectionString)
    
    Write-Host "Database opened successfully" -ForegroundColor Green
    
    # Test main tables
    $tables = @("Suppliers", "RawMaterials", "Categories", "Units")
    
    foreach ($table in $tables) {
        try {
            $recordset = New-Object -ComObject ADODB.Recordset
            $recordset.Open("SELECT COUNT(*) as RecordCount FROM [$table]", $connection)
            $count = $recordset.Fields.Item("RecordCount").Value
            $recordset.Close()
            Write-Host "Table $table has $count records" -ForegroundColor Green
        }
        catch {
            Write-Host "Error with table $table" -ForegroundColor Red
        }
    }
    
    # Test sample data
    try {
        $recordset = New-Object -ComObject ADODB.Recordset
        $recordset.Open("SELECT TOP 1 SupplierName FROM Suppliers", $connection)
        if (-not $recordset.EOF) {
            $name = $recordset.Fields.Item("SupplierName").Value
            Write-Host "Sample supplier: $name" -ForegroundColor Cyan
        }
        $recordset.Close()
    }
    catch {
        Write-Host "Error reading sample data" -ForegroundColor Red
    }
    
    Write-Host "Database is ready for forms and reports!" -ForegroundColor Green
    $connection.Close()
}
catch {
    Write-Host "Database test failed: $($_.Exception.Message)" -ForegroundColor Red
}
