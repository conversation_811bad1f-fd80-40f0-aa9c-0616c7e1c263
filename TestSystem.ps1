# Comprehensive Test Script for Factory Accounting System
Write-Host "=== Factory Accounting System Test ===" -ForegroundColor Green

# 1. Test file existence
Write-Host "`n1. Testing file existence:" -ForegroundColor Yellow

$requiredFiles = @(
    "Factory_Accounting_System.accdb",
    "CreateDB.ps1",
    "CreateTables.ps1"
)

foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "✓ $file exists" -ForegroundColor Green
    } else {
        Write-Host "✗ $file missing" -ForegroundColor Red
    }
}

# 2. Test database file properties
Write-Host "`n2. Testing database file:" -ForegroundColor Yellow

if (Test-Path "Factory_Accounting_System.accdb") {
    $dbFile = Get-Item "Factory_Accounting_System.accdb"
    $sizeKB = [math]::Round($dbFile.Length / 1KB, 2)
    Write-Host "✓ Database file size: $sizeKB KB" -ForegroundColor Green
    Write-Host "✓ Created: $($dbFile.CreationTime)" -ForegroundColor Green
    Write-Host "✓ Modified: $($dbFile.LastWriteTime)" -ForegroundColor Green
} else {
    Write-Host "✗ Database file not found" -ForegroundColor Red
}

# 3. Test database connection using OLEDB
Write-Host "`n3. Testing database connection:" -ForegroundColor Yellow

try {
    $connectionString = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=$((Get-Location).Path)\Factory_Accounting_System.accdb"
    $connection = New-Object -ComObject ADODB.Connection
    $connection.Open($connectionString)
    
    Write-Host "✓ Database connection successful" -ForegroundColor Green
    
    # Test basic query
    $recordset = New-Object -ComObject ADODB.Recordset
    $recordset.Open("SELECT Name FROM MSysObjects WHERE Type=1 AND Flags=0", $connection)
    
    $tableCount = 0
    $tables = @()
    while (-not $recordset.EOF) {
        $tableName = $recordset.Fields.Item("Name").Value
        if (-not $tableName.StartsWith("MSys")) {
            $tables += $tableName
            $tableCount++
        }
        $recordset.MoveNext()
    }
    $recordset.Close()
    
    Write-Host "✓ Found $tableCount user tables" -ForegroundColor Green
    
    # List tables
    Write-Host "`nTables found:"
    foreach ($table in $tables) {
        Write-Host "  - $table" -ForegroundColor Cyan
    }
    
    $connection.Close()
}
catch {
    Write-Host "✗ Database connection failed: $($_.Exception.Message)" -ForegroundColor Red
}

# 4. Test table structure
Write-Host "`n4. Testing table structure:" -ForegroundColor Yellow

$expectedTables = @(
    "Units", "Currencies", "Categories", "RawMaterials", "Suppliers",
    "PurchaseInvoices", "PurchaseInvoiceDetails", "Products", "ProductRecipes",
    "ProductionStages", "ProductionOrders", "RawMaterialsInventory",
    "FinishedProductsInventory", "InventoryMovements", "CostCenters", "ProductionCosts"
)

try {
    $connection = New-Object -ComObject ADODB.Connection
    $connection.Open($connectionString)
    
    foreach ($tableName in $expectedTables) {
        try {
            $recordset = New-Object -ComObject ADODB.Recordset
            $recordset.Open("SELECT TOP 1 * FROM [$tableName]", $connection)
            
            $fieldCount = $recordset.Fields.Count
            Write-Host "✓ Table $tableName exists with $fieldCount fields" -ForegroundColor Green
            
            $recordset.Close()
        }
        catch {
            Write-Host "✗ Table $tableName missing or inaccessible" -ForegroundColor Red
        }
    }
    
    $connection.Close()
}
catch {
    Write-Host "✗ Error testing table structure: $($_.Exception.Message)" -ForegroundColor Red
}

# 5. Test data insertion
Write-Host "`n5. Testing data insertion:" -ForegroundColor Yellow

try {
    $connection = New-Object -ComObject ADODB.Connection
    $connection.Open($connectionString)
    
    # Test inserting into Units table
    $command = New-Object -ComObject ADODB.Command
    $command.ActiveConnection = $connection
    $command.CommandText = "INSERT INTO Units (UnitName, Symbol) VALUES ('Test Unit', 'TU')"
    $command.Execute() | Out-Null
    Write-Host "✓ Successfully inserted test data into Units table" -ForegroundColor Green
    
    # Verify insertion
    $recordset = New-Object -ComObject ADODB.Recordset
    $recordset.Open("SELECT COUNT(*) as RecordCount FROM Units WHERE UnitName = 'Test Unit'", $connection)
    $count = $recordset.Fields.Item("RecordCount").Value
    $recordset.Close()
    
    if ($count -gt 0) {
        Write-Host "✓ Test data verified in Units table" -ForegroundColor Green
    } else {
        Write-Host "✗ Test data not found in Units table" -ForegroundColor Red
    }
    
    $connection.Close()
}
catch {
    Write-Host "✗ Data insertion test failed: $($_.Exception.Message)" -ForegroundColor Red
}

# 6. Test queries
Write-Host "`n6. Testing basic queries:" -ForegroundColor Yellow

try {
    $connection = New-Object -ComObject ADODB.Connection
    $connection.Open($connectionString)
    
    $testQueries = @{
        "Units" = "SELECT COUNT(*) as RecordCount FROM Units"
        "Currencies" = "SELECT COUNT(*) as RecordCount FROM Currencies"
        "Categories" = "SELECT COUNT(*) as RecordCount FROM Categories"
    }
    
    foreach ($table in $testQueries.Keys) {
        try {
            $recordset = New-Object -ComObject ADODB.Recordset
            $recordset.Open($testQueries[$table], $connection)
            $count = $recordset.Fields.Item("RecordCount").Value
            $recordset.Close()
            Write-Host "✓ Table $table contains $count records" -ForegroundColor Green
        }
        catch {
            Write-Host "✗ Query failed for table $table" -ForegroundColor Red
        }
    }
    
    $connection.Close()
}
catch {
    Write-Host "✗ Query testing failed: $($_.Exception.Message)" -ForegroundColor Red
}

# 7. Final report
Write-Host "`n=== Test Summary ===" -ForegroundColor Cyan
Write-Host "Database testing completed" -ForegroundColor White
Write-Host "Check messages above for detailed results" -ForegroundColor White

Write-Host "`nTest finished" -ForegroundColor Green
