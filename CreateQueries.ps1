# Create Arabic Queries in Access Database
Write-Host "Creating Arabic Queries in Access Database" -ForegroundColor Green

try {
    $connectionString = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=" + (Get-Location).Path + "\Factory_Accounting_System.accdb"
    $connection = New-Object -ComObject ADODB.Connection
    $connection.Open($connectionString)
    
    Write-Host "Database connection successful" -ForegroundColor Green
    
    # Create Suppliers Arabic Query
    Write-Host "Creating Suppliers Arabic Query..." -ForegroundColor Yellow
    
    try {
        $sql = "CREATE VIEW SuppliersArabicView AS SELECT s.SupplierID, s.SupplierName, s.ContactPerson, s.Phone, s.Mobile, s.Email, s.City, s.Country, s.Address, s.PaymentTerms, s.CreditLimit, s.AccountBalance, s.Notes, s.IsActive FROM Suppliers s ORDER BY s.SupplierName"
        $connection.Execute($sql)
        Write-Host "✓ Suppliers Arabic Query created" -ForegroundColor Green
    }
    catch {
        Write-Host "Suppliers query already exists" -ForegroundColor Yellow
    }
    
    # Create Raw Materials Arabic Query
    Write-Host "Creating Raw Materials Arabic Query..." -ForegroundColor Yellow
    
    try {
        $sql = "CREATE VIEW RawMaterialsArabicView AS SELECT rm.MaterialID, rm.MaterialName, rm.CategoryID, c.CategoryName, rm.UnitID, u.UnitName, rm.MinimumLevel, rm.AveragePrice, rm.Notes, rm.IsActive FROM (RawMaterials rm LEFT JOIN Categories c ON rm.CategoryID = c.CategoryID) LEFT JOIN Units u ON rm.UnitID = u.UnitID ORDER BY c.CategoryName, rm.MaterialName"
        $connection.Execute($sql)
        Write-Host "✓ Raw Materials Arabic Query created" -ForegroundColor Green
    }
    catch {
        Write-Host "Raw Materials query already exists" -ForegroundColor Yellow
    }
    
    # Create Inventory Report Query
    Write-Host "Creating Inventory Report Query..." -ForegroundColor Yellow
    
    try {
        $sql = "CREATE VIEW InventoryReportView AS SELECT rm.MaterialName, c.CategoryName, u.UnitName, rm.MinimumLevel, rm.AveragePrice, IIF(inv.AvailableQuantity IS NULL, 0, inv.AvailableQuantity) AS AvailableQuantity, IIF(inv.TotalValue IS NULL, 0, inv.TotalValue) AS TotalValue FROM ((RawMaterials rm LEFT JOIN Categories c ON rm.CategoryID = c.CategoryID) LEFT JOIN Units u ON rm.UnitID = u.UnitID) LEFT JOIN RawMaterialsInventory inv ON rm.MaterialID = inv.MaterialID WHERE rm.IsActive = True ORDER BY c.CategoryName, rm.MaterialName"
        $connection.Execute($sql)
        Write-Host "✓ Inventory Report Query created" -ForegroundColor Green
    }
    catch {
        Write-Host "Inventory Report query already exists" -ForegroundColor Yellow
    }
    
    # Create Purchase Invoices Query
    Write-Host "Creating Purchase Invoices Query..." -ForegroundColor Yellow
    
    try {
        $sql = "CREATE VIEW PurchaseInvoicesView AS SELECT pi.InvoiceID, pi.SupplierInvoiceNumber, s.SupplierName, pi.InvoiceDate, pi.DueDate, pi.SubTotal, pi.TaxAmount, pi.TotalAmount, pi.PaidAmount, pi.RemainingAmount, pi.InvoiceStatus FROM PurchaseInvoices pi LEFT JOIN Suppliers s ON pi.SupplierID = s.SupplierID ORDER BY pi.InvoiceDate DESC"
        $connection.Execute($sql)
        Write-Host "✓ Purchase Invoices Query created" -ForegroundColor Green
    }
    catch {
        Write-Host "Purchase Invoices query already exists" -ForegroundColor Yellow
    }
    
    # Create Production Cost Report Query
    Write-Host "Creating Production Cost Report Query..." -ForegroundColor Yellow
    
    try {
        $sql = "CREATE VIEW ProductionCostReportView AS SELECT po.OrderID, p.ProductName, po.RequiredQuantity, po.ProducedQuantity, po.EstimatedCost, po.ActualCost, po.OrderDate, po.OrderStatus FROM ProductionOrders po LEFT JOIN Products p ON po.ProductID = p.ProductID ORDER BY po.OrderDate DESC"
        $connection.Execute($sql)
        Write-Host "✓ Production Cost Report Query created" -ForegroundColor Green
    }
    catch {
        Write-Host "Production Cost Report query already exists" -ForegroundColor Yellow
    }
    
    # Test the queries
    Write-Host "`nTesting created queries..." -ForegroundColor Cyan
    
    $queries = @("SuppliersArabicView", "RawMaterialsArabicView", "InventoryReportView", "PurchaseInvoicesView", "ProductionCostReportView")
    
    foreach ($query in $queries) {
        try {
            $recordset = New-Object -ComObject ADODB.Recordset
            $recordset.Open("SELECT COUNT(*) as RecordCount FROM [$query]", $connection)
            $count = $recordset.Fields.Item("RecordCount").Value
            $recordset.Close()
            Write-Host "✓ $query : $count records" -ForegroundColor Green
        }
        catch {
            Write-Host "✗ $query : Error" -ForegroundColor Red
        }
    }
    
    Write-Host "`n=== Queries Created Successfully! ===" -ForegroundColor Cyan
    Write-Host "✓ SuppliersArabicView" -ForegroundColor Green
    Write-Host "✓ RawMaterialsArabicView" -ForegroundColor Green
    Write-Host "✓ InventoryReportView" -ForegroundColor Green
    Write-Host "✓ PurchaseInvoicesView" -ForegroundColor Green
    Write-Host "✓ ProductionCostReportView" -ForegroundColor Green
    
    Write-Host "`nNow you can:" -ForegroundColor Yellow
    Write-Host "1. Open Access and refresh Navigation Pane (F5)" -ForegroundColor White
    Write-Host "2. Use Form Wizard with these queries" -ForegroundColor White
    Write-Host "3. Use Report Wizard with these queries" -ForegroundColor White
    Write-Host "4. Create navigation forms" -ForegroundColor White
    
    $connection.Close()
}
catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}
