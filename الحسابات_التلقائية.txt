كود VBA للحسابات التلقائية وتحديث المخزون
=============================================

يجب نسخ هذا الكود في وحدة VBA جديدة في Access:

' إجراء تحديث مخزون المواد الخام عند الشراء
Public Sub UpdateRawMaterialInventoryPurchase(MaterialID As Long, Quantity As Double, UnitPrice As Currency, InvoiceID As Long)
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim CurrentQuantity As Double
    Dim CurrentValue As Currency
    Dim NewQuantity As Double
    Dim NewAverageCost As Currency
    Dim NewTotalValue As Currency
    
    Set db = CurrentDb
    
    ' الحصول على الكميات والقيم الحالية
    Set rs = db.OpenRecordset("SELECT * FROM RawMaterialsInventory WHERE MaterialID = " & MaterialID)
    
    If rs.EOF Then
        ' إنشاء سجل جديد إذا لم يكن موجوداً
        rs.AddNew
        rs!MaterialID = MaterialID
        rs!AvailableQuantity = Quantity
        rs!AverageCost = UnitPrice
        rs!TotalValue = Quantity * UnitPrice
        rs!LastMovementDate = Now()
        rs.Update
        
        CurrentQuantity = 0
        NewQuantity = Quantity
    Else
        ' تحديث السجل الموجود
        CurrentQuantity = Nz(rs!AvailableQuantity, 0)
        CurrentValue = Nz(rs!TotalValue, 0)
        
        NewQuantity = CurrentQuantity + Quantity
        NewTotalValue = CurrentValue + (Quantity * UnitPrice)
        NewAverageCost = NewTotalValue / NewQuantity
        
        rs.Edit
        rs!AvailableQuantity = NewQuantity
        rs!AverageCost = NewAverageCost
        rs!TotalValue = NewTotalValue
        rs!LastMovementDate = Now()
        rs.Update
    End If
    
    rs.Close
    
    ' إدراج حركة المخزون
    InsertInventoryMovement "دخول", "مادة خام", MaterialID, Quantity, UnitPrice, _
                           Quantity * UnitPrice, InvoiceID, "فاتورة شراء", _
                           CurrentQuantity, NewQuantity
    
    Set rs = Nothing
    Set db = Nothing
End Sub

' إجراء تحديث مخزون المواد الخام عند الاستهلاك في الإنتاج
Public Sub UpdateRawMaterialInventoryConsumption(MaterialID As Long, ConsumedQuantity As Double, ProductionOrderID As Long)
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim CurrentQuantity As Double
    Dim CurrentAverageCost As Currency
    Dim NewQuantity As Double
    Dim NewTotalValue As Currency
    
    Set db = CurrentDb
    
    ' الحصول على الكميات والقيم الحالية
    Set rs = db.OpenRecordset("SELECT * FROM RawMaterialsInventory WHERE MaterialID = " & MaterialID)
    
    If rs.EOF Then
        MsgBox "المادة الخام غير موجودة في المخزون!", vbCritical
        Exit Sub
    End If
    
    CurrentQuantity = Nz(rs!AvailableQuantity, 0)
    CurrentAverageCost = Nz(rs!AverageCost, 0)
    
    ' التحقق من توفر الكمية
    If CurrentQuantity < ConsumedQuantity Then
        MsgBox "الكمية المطلوبة (" & ConsumedQuantity & ") غير متوفرة في المخزون! الكمية المتاحة: " & CurrentQuantity, vbCritical
        Exit Sub
    End If
    
    ' حساب القيم الجديدة
    NewQuantity = CurrentQuantity - ConsumedQuantity
    NewTotalValue = NewQuantity * CurrentAverageCost
    
    ' تحديث المخزون
    rs.Edit
    rs!AvailableQuantity = NewQuantity
    rs!TotalValue = NewTotalValue
    rs!LastMovementDate = Now()
    rs.Update
    
    rs.Close
    
    ' إدراج حركة المخزون
    InsertInventoryMovement "خروج", "مادة خام", MaterialID, ConsumedQuantity, CurrentAverageCost, _
                           ConsumedQuantity * CurrentAverageCost, ProductionOrderID, "أمر إنتاج", _
                           CurrentQuantity, NewQuantity
    
    Set rs = Nothing
    Set db = Nothing
End Sub

' إجراء إضافة منتج نهائي للمخزون
Public Sub AddFinishedProductToInventory(ProductID As Long, ProducedQuantity As Double, ProductionCost As Currency, ProductionOrderID As Long)
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim CurrentQuantity As Double
    Dim CurrentValue As Currency
    Dim NewQuantity As Double
    Dim NewAverageCost As Currency
    Dim NewTotalValue As Currency
    
    Set db = CurrentDb
    
    ' الحصول على الكميات والقيم الحالية
    Set rs = db.OpenRecordset("SELECT * FROM FinishedProductsInventory WHERE ProductID = " & ProductID)
    
    If rs.EOF Then
        ' إنشاء سجل جديد إذا لم يكن موجوداً
        rs.AddNew
        rs!ProductID = ProductID
        rs!AvailableQuantity = ProducedQuantity
        rs!AverageCost = ProductionCost
        rs!TotalValue = ProducedQuantity * ProductionCost
        rs!LastMovementDate = Now()
        rs.Update
        
        CurrentQuantity = 0
        NewQuantity = ProducedQuantity
    Else
        ' تحديث السجل الموجود
        CurrentQuantity = Nz(rs!AvailableQuantity, 0)
        CurrentValue = Nz(rs!TotalValue, 0)
        
        NewQuantity = CurrentQuantity + ProducedQuantity
        NewTotalValue = CurrentValue + (ProducedQuantity * ProductionCost)
        NewAverageCost = NewTotalValue / NewQuantity
        
        rs.Edit
        rs!AvailableQuantity = NewQuantity
        rs!AverageCost = NewAverageCost
        rs!TotalValue = NewTotalValue
        rs!LastMovementDate = Now()
        rs.Update
    End If
    
    rs.Close
    
    ' إدراج حركة المخزون
    InsertInventoryMovement "دخول", "منتج نهائي", ProductID, ProducedQuantity, ProductionCost, _
                           ProducedQuantity * ProductionCost, ProductionOrderID, "أمر إنتاج", _
                           CurrentQuantity, NewQuantity
    
    Set rs = Nothing
    Set db = Nothing
End Sub

' إجراء إدراج حركة المخزون
Private Sub InsertInventoryMovement(MovementType As String, ItemType As String, ItemID As Long, _
                                   Quantity As Double, UnitPrice As Currency, TotalValue As Currency, _
                                   ReferenceNumber As Long, ReferenceType As String, _
                                   BalanceBefore As Double, BalanceAfter As Double)
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    
    Set db = CurrentDb
    Set rs = db.OpenRecordset("InventoryMovements")
    
    rs.AddNew
    rs!MovementType = MovementType
    rs!ItemType = ItemType
    rs!ItemID = ItemID
    rs!Quantity = Quantity
    rs!UnitPrice = UnitPrice
    rs!TotalValue = TotalValue
    rs!MovementDate = Now()
    rs!ReferenceNumber = ReferenceNumber
    rs!ReferenceType = ReferenceType
    rs!BalanceBefore = BalanceBefore
    rs!BalanceAfter = BalanceAfter
    rs!EntryUser = Environ("USERNAME")
    rs.Update
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
End Sub

' إجراء حساب تكلفة الإنتاج التلقائية
Public Sub CalculateProductionCost(ProductionOrderID As Long)
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim TotalMaterialCost As Currency
    Dim TotalLaborCost As Currency
    Dim TotalOverheadCost As Currency
    Dim TotalCost As Currency
    
    Set db = CurrentDb
    
    ' حساب تكلفة المواد الخام
    Set rs = db.OpenRecordset("SELECT SUM(ConsumedQuantity * UnitCost) AS MaterialCost " & _
                             "FROM ProductionOrderDetails WHERE OrderID = " & ProductionOrderID)
    If Not rs.EOF Then
        TotalMaterialCost = Nz(rs!MaterialCost, 0)
    End If
    rs.Close
    
    ' حساب تكاليف العمالة والتكاليف غير المباشرة
    Set rs = db.OpenRecordset("SELECT CostType, SUM(CostAmount) AS TotalAmount " & _
                             "FROM ProductionCosts WHERE OrderID = " & ProductionOrderID & _
                             " GROUP BY CostType")
    
    While Not rs.EOF
        Select Case rs!CostType
            Case "عمالة"
                TotalLaborCost = TotalLaborCost + Nz(rs!TotalAmount, 0)
            Case "تكاليف غير مباشرة"
                TotalOverheadCost = TotalOverheadCost + Nz(rs!TotalAmount, 0)
        End Select
        rs.MoveNext
    Wend
    rs.Close
    
    ' حساب إجمالي التكلفة
    TotalCost = TotalMaterialCost + TotalLaborCost + TotalOverheadCost
    
    ' تحديث أمر الإنتاج
    Set rs = db.OpenRecordset("SELECT * FROM ProductionOrders WHERE OrderID = " & ProductionOrderID)
    If Not rs.EOF Then
        rs.Edit
        rs!ActualCost = TotalCost
        rs.Update
    End If
    rs.Close
    
    Set rs = Nothing
    Set db = Nothing
End Sub

' إجراء تحديث أرصدة الموردين
Public Sub UpdateSupplierBalance(SupplierID As Long, Amount As Currency, TransactionType As String)
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim CurrentBalance As Currency
    
    Set db = CurrentDb
    Set rs = db.OpenRecordset("SELECT * FROM Suppliers WHERE SupplierID = " & SupplierID)
    
    If Not rs.EOF Then
        CurrentBalance = Nz(rs!AccountBalance, 0)
        
        rs.Edit
        If TransactionType = "فاتورة" Then
            rs!AccountBalance = CurrentBalance + Amount
        ElseIf TransactionType = "دفعة" Then
            rs!AccountBalance = CurrentBalance - Amount
        End If
        rs.Update
    End If
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
End Sub

' إجراء التحقق من الحد الأدنى للمخزون
Public Sub CheckMinimumInventoryLevels()
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim AlertMessage As String
    
    Set db = CurrentDb
    
    ' فحص المواد الخام
    Set rs = db.OpenRecordset("SELECT rm.MaterialName, inv.AvailableQuantity, rm.MinimumLevel " & _
                             "FROM RawMaterials rm INNER JOIN RawMaterialsInventory inv " & _
                             "ON rm.MaterialID = inv.MaterialID " & _
                             "WHERE inv.AvailableQuantity <= rm.MinimumLevel AND rm.IsActive = True")
    
    If Not rs.EOF Then
        AlertMessage = "تحذير: المواد التالية تحت الحد الأدنى:" & vbCrLf & vbCrLf
        While Not rs.EOF
            AlertMessage = AlertMessage & "- " & rs!MaterialName & " (متاح: " & rs!AvailableQuantity & ", الحد الأدنى: " & rs!MinimumLevel & ")" & vbCrLf
            rs.MoveNext
        Wend
        MsgBox AlertMessage, vbExclamation, "تحذير المخزون"
    End If
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
End Sub
