' سكريبت VBScript لإنشاء جداول قاعدة البيانات
Dim accessApp, db, sql

' إنشاء كائن Access
Set accessApp = CreateObject("Access.Application")
accessApp.Visible = False

' فتح قاعدة البيانات
accessApp.OpenCurrentDatabase "D:\QQ@WORK1\الاجازات\23\نظام_محاسبة_المصنع.accdb"
Set db = accessApp.CurrentDb

' إنشاء جدول وحدات القياس
sql = "CREATE TABLE وحدات_القياس (" & _
      "رقم_الوحدة AUTOINCREMENT PRIMARY KEY, " & _
      "اسم_الوحدة TEXT(50) NOT NULL, " & _
      "الرمز TEXT(10), " & _
      "ملاحظات MEMO)"
db.Execute sql

' إنشاء جدول العملات
sql = "CREATE TABLE العملات (" & _
      "رقم_العملة AUTOINCREMENT PRIMARY KEY, " & _
      "اسم_العملة TEXT(50) NOT NULL, " & _
      "الرمز TEXT(10), " & _
      "سعر_الصرف CURRENCY DEFAULT 1, " & _
      "تاريخ_التحديث DATETIME DEFAULT Now())"
db.Execute sql

' إنشاء جدول فئات المواد
sql = "CREATE TABLE فئات_المواد (" & _
      "رقم_الفئة AUTOINCREMENT PRIMARY KEY, " & _
      "اسم_الفئة TEXT(100) NOT NULL, " & _
      "وصف_الفئة MEMO, " & _
      "نوع_الفئة TEXT(20))"
db.Execute sql

' إنشاء جدول المواد الخام
sql = "CREATE TABLE المواد_الخام (" & _
      "رقم_المادة AUTOINCREMENT PRIMARY KEY, " & _
      "اسم_المادة TEXT(100) NOT NULL, " & _
      "رقم_الفئة LONG, " & _
      "رقم_الوحدة LONG, " & _
      "الحد_الأدنى DOUBLE DEFAULT 0, " & _
      "متوسط_السعر CURRENCY DEFAULT 0, " & _
      "ملاحظات MEMO, " & _
      "تاريخ_الإنشاء DATETIME DEFAULT Now(), " & _
      "نشط YESNO DEFAULT True)"
db.Execute sql

' إنشاء جدول الموردين
sql = "CREATE TABLE الموردين (" & _
      "رقم_المورد AUTOINCREMENT PRIMARY KEY, " & _
      "اسم_المورد TEXT(100) NOT NULL, " & _
      "اسم_جهة_الاتصال TEXT(100), " & _
      "الهاتف TEXT(20), " & _
      "الجوال TEXT(20), " & _
      "البريد_الإلكتروني TEXT(100), " & _
      "العنوان MEMO, " & _
      "المدينة TEXT(50), " & _
      "الدولة TEXT(50), " & _
      "الرقم_الضريبي TEXT(50), " & _
      "شروط_الدفع TEXT(100), " & _
      "مدة_الائتمان INTEGER DEFAULT 0, " & _
      "حد_الائتمان CURRENCY DEFAULT 0, " & _
      "رصيد_الحساب CURRENCY DEFAULT 0, " & _
      "تاريخ_التسجيل DATETIME DEFAULT Now(), " & _
      "نشط YESNO DEFAULT True, " & _
      "ملاحظات MEMO)"
db.Execute sql

' إنشاء جدول فواتير الشراء
sql = "CREATE TABLE فواتير_الشراء (" & _
      "رقم_الفاتورة AUTOINCREMENT PRIMARY KEY, " & _
      "رقم_فاتورة_المورد TEXT(50), " & _
      "رقم_المورد LONG NOT NULL, " & _
      "تاريخ_الفاتورة DATETIME DEFAULT Now(), " & _
      "تاريخ_الاستحقاق DATETIME, " & _
      "رقم_العملة LONG DEFAULT 1, " & _
      "سعر_الصرف CURRENCY DEFAULT 1, " & _
      "إجمالي_قبل_الضريبة CURRENCY DEFAULT 0, " & _
      "قيمة_الضريبة CURRENCY DEFAULT 0, " & _
      "نسبة_الضريبة DOUBLE DEFAULT 0, " & _
      "إجمالي_الفاتورة CURRENCY DEFAULT 0, " & _
      "المبلغ_المدفوع CURRENCY DEFAULT 0, " & _
      "المبلغ_المتبقي CURRENCY DEFAULT 0, " & _
      "حالة_الفاتورة TEXT(20) DEFAULT 'مفتوحة', " & _
      "ملاحظات MEMO, " & _
      "تاريخ_الإدخال DATETIME DEFAULT Now(), " & _
      "مستخدم_الإدخال TEXT(50))"
db.Execute sql

' إنشاء جدول تفاصيل فواتير الشراء
sql = "CREATE TABLE تفاصيل_فواتير_الشراء (" & _
      "رقم_التفصيل AUTOINCREMENT PRIMARY KEY, " & _
      "رقم_الفاتورة LONG NOT NULL, " & _
      "رقم_المادة LONG NOT NULL, " & _
      "الكمية DOUBLE NOT NULL, " & _
      "سعر_الوحدة CURRENCY NOT NULL, " & _
      "إجمالي_السطر CURRENCY, " & _
      "نسبة_الخصم DOUBLE DEFAULT 0, " & _
      "قيمة_الخصم CURRENCY DEFAULT 0, " & _
      "الصافي CURRENCY, " & _
      "تاريخ_الانتهاء DATETIME, " & _
      "رقم_اللوط TEXT(50), " & _
      "ملاحظات MEMO)"
db.Execute sql

' إنشاء جدول المنتجات
sql = "CREATE TABLE المنتجات (" & _
      "رقم_المنتج AUTOINCREMENT PRIMARY KEY, " & _
      "اسم_المنتج TEXT(100) NOT NULL, " & _
      "رقم_الفئة LONG, " & _
      "رقم_الوحدة LONG, " & _
      "سعر_البيع CURRENCY DEFAULT 0, " & _
      "تكلفة_الإنتاج CURRENCY DEFAULT 0, " & _
      "وقت_الإنتاج INTEGER DEFAULT 0, " & _
      "الحد_الأدنى DOUBLE DEFAULT 0, " & _
      "وصف_المنتج MEMO, " & _
      "تاريخ_الإنشاء DATETIME DEFAULT Now(), " & _
      "نشط YESNO DEFAULT True)"
db.Execute sql

WScript.Echo "تم إنشاء الجداول الأساسية بنجاح"

' إغلاق قاعدة البيانات
accessApp.CloseCurrentDatabase
accessApp.Quit
Set db = Nothing
Set accessApp = Nothing
