# تعليمات تطبيق الواجهة العربية - خطوات سريعة

## 🚀 خطوات التطبيق السريع

### الخطوة 1: إنشاء الاستعلامات العربية

1. **افتح Microsoft Access**
2. **انتقل إلى Create > Query Design**
3. **أغلق نافذة Show Table**
4. **انتقل إلى SQL View**
5. **انسخ والصق الكود التالي لكل استعلام:**

#### استعلام تقرير المخزون:
```sql
SELECT 
    rm.MaterialName AS [اسم المادة],
    c.CategoryName AS [الفئة],
    u.UnitName AS [وحدة القياس],
    IIF(inv.AvailableQuantity IS NULL, 0, inv.AvailableQuantity) AS [الكمية المتاحة],
    IIF(inv.AverageCost IS NULL, 0, inv.AverageCost) AS [متوسط التكلفة],
    IIF(inv.TotalValue IS NULL, 0, inv.TotalValue) AS [إجمالي القيمة],
    rm.MinimumLevel AS [الحد الأدنى],
    IIF(IIF(inv.AvailableQuantity IS NULL, 0, inv.AvailableQuantity) <= rm.MinimumLevel, "تحت الحد الأدنى", "مناسب") AS [حالة المخزون]
FROM ((RawMaterials rm 
LEFT JOIN Categories c ON rm.CategoryID = c.CategoryID) 
LEFT JOIN Units u ON rm.UnitID = u.UnitID) 
LEFT JOIN RawMaterialsInventory inv ON rm.MaterialID = inv.MaterialID
ORDER BY c.CategoryName, rm.MaterialName
```
**احفظ باسم**: `تقرير_المخزون_الشامل`

#### استعلام تقرير الموردين:
```sql
SELECT 
    s.SupplierName AS [اسم المورد],
    s.ContactPerson AS [جهة الاتصال],
    s.Phone AS [الهاتف],
    s.Email AS [البريد الإلكتروني],
    s.City AS [المدينة],
    s.CreditLimit AS [حد الائتمان],
    s.AccountBalance AS [رصيد الحساب],
    COUNT(pi.InvoiceID) AS [عدد الفواتير],
    IIF(SUM(pi.TotalAmount) IS NULL, 0, SUM(pi.TotalAmount)) AS [إجمالي المشتريات],
    IIF(s.IsActive, "نشط", "غير نشط") AS [الحالة]
FROM Suppliers s 
LEFT JOIN PurchaseInvoices pi ON s.SupplierID = pi.SupplierID
GROUP BY s.SupplierID, s.SupplierName, s.ContactPerson, s.Phone, s.Email, s.City, s.CreditLimit, s.AccountBalance, s.IsActive
ORDER BY [إجمالي المشتريات] DESC
```
**احفظ باسم**: `تقرير_الموردين_والأرصدة`

#### استعلام تحليل الأرباح:
```sql
SELECT 
    p.ProductName AS [اسم المنتج],
    c.CategoryName AS [فئة المنتج],
    p.SalePrice AS [سعر البيع],
    p.ProductionCost AS [تكلفة الإنتاج],
    (p.SalePrice - p.ProductionCost) AS [هامش الربح],
    IIF(p.ProductionCost > 0, ((p.SalePrice - p.ProductionCost) / p.ProductionCost) * 100, 0) AS [نسبة الربح %],
    IIF(p.IsActive, "نشط", "غير نشط") AS [حالة المنتج]
FROM (Products p 
LEFT JOIN Categories c ON p.CategoryID = c.CategoryID)
WHERE p.IsActive = True
ORDER BY [نسبة الربح %] DESC
```
**احفظ باسم**: `تقرير_تحليل_الأرباح`

### الخطوة 2: إنشاء النماذج العربية

#### أ. إنشاء النموذج الرئيسي:
1. **Create > Form Design**
2. **اضبط خصائص النموذج:**
   - Caption: `نظام محاسبة مصنع المعمول والمخللات`
   - Right To Left: `Yes`
   - Right To Left Layout: `Yes`
   - Allow Additions: `No`
   - Allow Deletions: `No`
   - Allow Edits: `No`
   - Navigation Buttons: `No`

3. **أضف العناصر التالية:**
   - **عنوان رئيسي**: "نظام محاسبة مصنع المعمول والمخللات"
   - **أزرار التنقل**:
     - إدارة الموردين
     - إدارة المواد الخام
     - فواتير الشراء
     - أوامر الإنتاج
     - تقارير المخزون
     - تقارير الموردين

4. **احفظ باسم**: `القائمة_الرئيسية`

#### ب. إنشاء نموذج الموردين:
1. **Create > Form Wizard**
2. **اختر جدول**: `Suppliers`
3. **اختر جميع الحقول**
4. **اختر تخطيط**: `Columnar`
5. **اضبط خصائص النموذج:**
   - Caption: `إدارة الموردين`
   - Right To Left: `Yes`
   - Right To Left Layout: `Yes`

6. **غير تسميات الحقول إلى:**
   - SupplierName → اسم المورد
   - ContactPerson → جهة الاتصال
   - Phone → الهاتف
   - Mobile → الجوال
   - Email → البريد الإلكتروني
   - City → المدينة
   - Country → الدولة
   - Address → العنوان
   - PaymentTerms → شروط الدفع
   - CreditLimit → حد الائتمان
   - AccountBalance → رصيد الحساب

7. **احفظ باسم**: `نموذج_الموردين`

### الخطوة 3: إنشاء التقارير العربية

#### أ. تقرير المخزون:
1. **Create > Report Wizard**
2. **اختر استعلام**: `تقرير_المخزون_الشامل`
3. **اختر جميع الحقول**
4. **اضبط خصائص التقرير:**
   - Caption: `تقرير المخزون الشامل`
   - Right To Left: `Yes`
   - Right To Left Layout: `Yes`

5. **في Design View:**
   - **أضف عنوان**: "تقرير المخزون الشامل - مصنع المعمول والمخللات"
   - **أضف تاريخ**: `=Now()`
   - **أضف رقم صفحة**: `="صفحة " & [Page] & " من " & [Pages]`

6. **احفظ باسم**: `تقرير_المخزون_الشامل`

#### ب. تقرير الموردين:
1. **Create > Report Wizard**
2. **اختر استعلام**: `تقرير_الموردين_والأرصدة`
3. **اختر الحقول المطلوبة**
4. **اضبط نفس الخصائص السابقة**
5. **احفظ باسم**: `تقرير_الموردين_والأرصدة`

### الخطوة 4: ضبط الخطوط والتنسيق

#### لجميع النماذج والتقارير:
1. **اختر جميع العناصر** (Ctrl+A)
2. **اضبط الخط:**
   - Font Name: `Tahoma`
   - Font Size: `11`
   - Text Align: `Right` (للنصوص العربية)

3. **للأرقام والعملات:**
   - Text Align: `Right`
   - Format: `Currency` (للمبالغ)
   - Format: `Standard` (للكميات)

### الخطوة 5: إضافة أزرار التنقل

#### في النموذج الرئيسي:
1. **أضف أزرار Command Button**
2. **اضبط خصائص كل زر:**
   - Caption: النص العربي
   - On Click: `[Event Procedure]`

3. **أضف كود VBA لكل زر:**
```vba
Private Sub btnSuppliers_Click()
    DoCmd.OpenForm "نموذج_الموردين"
End Sub

Private Sub btnInventoryReport_Click()
    DoCmd.OpenReport "تقرير_المخزون_الشامل", acViewPreview
End Sub

Private Sub btnSuppliersReport_Click()
    DoCmd.OpenReport "تقرير_الموردين_والأرصدة", acViewPreview
End Sub
```

### الخطوة 6: اختبار الواجهة

#### اختبار النماذج:
1. **افتح كل نموذج**
2. **أدخل نصوص عربية**
3. **تحقق من الاتجاه الصحيح**
4. **اختبر الحفظ والتعديل**

#### اختبار التقارير:
1. **افتح كل تقرير في Preview**
2. **تحقق من التنسيق**
3. **اختبر الطباعة**
4. **تحقق من عرض البيانات العربية**

#### اختبار الاستعلامات:
1. **شغل كل استعلام**
2. **تحقق من أسماء الأعمدة العربية**
3. **تحقق من صحة البيانات**

## ✅ قائمة التحقق النهائية

### الاستعلامات:
- [ ] تقرير_المخزون_الشامل
- [ ] تقرير_الموردين_والأرصدة  
- [ ] تقرير_تحليل_الأرباح

### النماذج:
- [ ] القائمة_الرئيسية
- [ ] نموذج_الموردين
- [ ] خصائص RTL مضبوطة
- [ ] خط Tahoma مطبق

### التقارير:
- [ ] تقرير_المخزون_الشامل
- [ ] تقرير_الموردين_والأرصدة
- [ ] تنسيق RTL مضبوط
- [ ] عناوين عربية

### الاختبار:
- [ ] إدخال نصوص عربية
- [ ] عرض التقارير
- [ ] طباعة التقارير
- [ ] التنقل بين النماذج

## 🎯 نصائح مهمة

1. **استخدم خط Tahoma دائماً** للنصوص العربية
2. **اضبط Right To Left = Yes** لجميع النماذج والتقارير
3. **اختبر الطباعة** قبل الاستخدام النهائي
4. **احفظ نسخة احتياطية** قبل التعديل
5. **اختبر مع بيانات حقيقية** للتأكد من الأداء

---

**بهذه الخطوات ستحصل على واجهة عربية متكاملة وجاهزة للاستخدام!** 🎉
