# Create Views for Forms and Reports
Write-Host "Creating Views in Access Database" -ForegroundColor Green

try {
    $dbPath = (Get-Location).Path + "\Factory_Accounting_System.accdb"
    $connectionString = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=$dbPath"
    $connection = New-Object -ComObject ADODB.Connection
    $connection.Open($connectionString)
    
    Write-Host "Database opened successfully" -ForegroundColor Green
    
    # Create Suppliers View
    try {
        $sql = "CREATE VIEW SuppliersView AS SELECT SupplierID, SupplierName, ContactPerson, Phone, Mobile, Email, City, Country, Address, PaymentTerms, CreditLimit, AccountBalance, Notes, IsActive FROM Suppliers ORDER BY SupplierName"
        $connection.Execute($sql)
        Write-Host "Suppliers View created" -ForegroundColor Green
    }
    catch {
        Write-Host "Suppliers View already exists" -ForegroundColor Yellow
    }
    
    # Create Raw Materials View
    try {
        $sql = "CREATE VIEW RawMaterialsView AS SELECT rm.MaterialID, rm.MaterialName, rm.CategoryID, c.CategoryName, rm.UnitID, u.UnitName, rm.MinimumLevel, rm.AveragePrice, rm.Notes, rm.IsActive FROM (RawMaterials rm LEFT JOIN Categories c ON rm.CategoryID = c.CategoryID) LEFT JOIN Units u ON rm.UnitID = u.UnitID ORDER BY c.CategoryName, rm.MaterialName"
        $connection.Execute($sql)
        Write-Host "Raw Materials View created" -ForegroundColor Green
    }
    catch {
        Write-Host "Raw Materials View already exists" -ForegroundColor Yellow
    }
    
    # Create Inventory Report View
    try {
        $sql = "CREATE VIEW InventoryReportView AS SELECT rm.MaterialName, c.CategoryName, u.UnitName, rm.MinimumLevel, rm.AveragePrice FROM (RawMaterials rm LEFT JOIN Categories c ON rm.CategoryID = c.CategoryID) LEFT JOIN Units u ON rm.UnitID = u.UnitID WHERE rm.IsActive = True ORDER BY c.CategoryName, rm.MaterialName"
        $connection.Execute($sql)
        Write-Host "Inventory Report View created" -ForegroundColor Green
    }
    catch {
        Write-Host "Inventory Report View already exists" -ForegroundColor Yellow
    }
    
    # Test views
    $views = @("SuppliersView", "RawMaterialsView", "InventoryReportView")
    
    foreach ($view in $views) {
        try {
            $recordset = New-Object -ComObject ADODB.Recordset
            $recordset.Open("SELECT COUNT(*) as RecordCount FROM [$view]", $connection)
            $count = $recordset.Fields.Item("RecordCount").Value
            $recordset.Close()
            Write-Host "$view has $count records" -ForegroundColor Green
        }
        catch {
            Write-Host "Error with $view" -ForegroundColor Red
        }
    }
    
    Write-Host "Views created successfully!" -ForegroundColor Green
    $connection.Close()
}
catch {
    Write-Host "Error occurred" -ForegroundColor Red
}
