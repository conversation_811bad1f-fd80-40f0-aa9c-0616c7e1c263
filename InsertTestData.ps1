# Insert Test Data
Write-Host "Inserting Test Data" -ForegroundColor Green

try {
    $connectionString = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=" + (Get-Location).Path + "\Factory_Accounting_System.accdb"
    $connection = New-Object -ComObject ADODB.Connection
    $connection.Open($connectionString)
    
    Write-Host "Database connection successful" -ForegroundColor Green
    
    # Insert Units
    $sql = "INSERT INTO Units (UnitName, Symbol) VALUES ('Kilogram', 'KG')"
    $connection.Execute($sql)
    $sql = "INSERT INTO Units (UnitName, Symbol) VALUES ('Gram', 'G')"
    $connection.Execute($sql)
    $sql = "INSERT INTO Units (UnitName, Symbol) VALUES ('Liter', 'L')"
    $connection.Execute($sql)
    $sql = "INSERT INTO Units (UnitName, Symbol) VALUES ('Piece', 'PC')"
    $connection.Execute($sql)
    Write-Host "Units data inserted" -ForegroundColor Green
    
    # Insert Currencies
    $sql = "INSERT INTO Currencies (CurrencyName, Symbol, ExchangeRate) VALUES ('Saudi Riyal', 'SAR', 1.00)"
    $connection.Execute($sql)
    $sql = "INSERT INTO Currencies (CurrencyName, Symbol, ExchangeRate) VALUES ('US Dollar', 'USD', 3.75)"
    $connection.Execute($sql)
    Write-Host "Currencies data inserted" -ForegroundColor Green
    
    # Insert Categories
    $sql = "INSERT INTO Categories (CategoryName, Description, CategoryType) VALUES ('Flour and Starches', 'Flour and starches used in manufacturing', 'Raw Materials')"
    $connection.Execute($sql)
    $sql = "INSERT INTO Categories (CategoryName, Description, CategoryType) VALUES ('Sugar and Sweeteners', 'Sugar and natural and artificial sweeteners', 'Raw Materials')"
    $connection.Execute($sql)
    $sql = "INSERT INTO Categories (CategoryName, Description, CategoryType) VALUES ('Oils and Fats', 'Oils and fats used in cooking', 'Raw Materials')"
    $connection.Execute($sql)
    $sql = "INSERT INTO Categories (CategoryName, Description, CategoryType) VALUES ('Maamoul Products', 'Various types of Maamoul', 'Finished Products')"
    $connection.Execute($sql)
    $sql = "INSERT INTO Categories (CategoryName, Description, CategoryType) VALUES ('Pickles Products', 'Various types of pickles', 'Finished Products')"
    $connection.Execute($sql)
    Write-Host "Categories data inserted" -ForegroundColor Green
    
    # Insert Raw Materials
    $sql = "INSERT INTO RawMaterials (MaterialName, CategoryID, UnitID, MinimumLevel, AveragePrice, IsActive) VALUES ('White Flour Premium', 1, 1, 100, 2.50, True)"
    $connection.Execute($sql)
    $sql = "INSERT INTO RawMaterials (MaterialName, CategoryID, UnitID, MinimumLevel, AveragePrice, IsActive) VALUES ('Brown Flour', 1, 1, 50, 3.00, True)"
    $connection.Execute($sql)
    $sql = "INSERT INTO RawMaterials (MaterialName, CategoryID, UnitID, MinimumLevel, AveragePrice, IsActive) VALUES ('White Sugar', 2, 1, 200, 2.80, True)"
    $connection.Execute($sql)
    $sql = "INSERT INTO RawMaterials (MaterialName, CategoryID, UnitID, MinimumLevel, AveragePrice, IsActive) VALUES ('Olive Oil', 3, 3, 30, 25.00, True)"
    $connection.Execute($sql)
    Write-Host "Raw Materials data inserted" -ForegroundColor Green
    
    # Insert Suppliers
    $sql = "INSERT INTO Suppliers (SupplierName, ContactPerson, Phone, Email, City, PaymentTerms, CreditLimit, IsActive) VALUES ('Golden Flour Company', 'Ahmed Mohammed', '011-4567890', '<EMAIL>', 'Riyadh', '30 days credit', 50000, True)"
    $connection.Execute($sql)
    $sql = "INSERT INTO Suppliers (SupplierName, ContactPerson, Phone, Email, City, PaymentTerms, CreditLimit, IsActive) VALUES ('Sugar and Sweeteners Co', 'Fatima Ali', '011-7654321', '<EMAIL>', 'Dammam', '45 days credit', 75000, True)"
    $connection.Execute($sql)
    Write-Host "Suppliers data inserted" -ForegroundColor Green
    
    # Insert Products
    $sql = "INSERT INTO Products (ProductName, CategoryID, UnitID, SalePrice, ProductionCost, MinimumLevel, IsActive) VALUES ('Maamoul with Dates - Small', 4, 4, 25.00, 18.50, 50, True)"
    $connection.Execute($sql)
    $sql = "INSERT INTO Products (ProductName, CategoryID, UnitID, SalePrice, ProductionCost, MinimumLevel, IsActive) VALUES ('Maamoul with Dates - Large', 4, 4, 45.00, 32.00, 30, True)"
    $connection.Execute($sql)
    $sql = "INSERT INTO Products (ProductName, CategoryID, UnitID, SalePrice, ProductionCost, MinimumLevel, IsActive) VALUES ('Cucumber Pickles', 5, 4, 12.00, 8.50, 100, True)"
    $connection.Execute($sql)
    Write-Host "Products data inserted" -ForegroundColor Green
    
    # Insert Production Stages
    $sql = "INSERT INTO ProductionStages (StageName, Description, StageOrder, StageCost, StageTime) VALUES ('Preparation and Mixing', 'Prepare and mix basic ingredients', 1, 5.00, 30)"
    $connection.Execute($sql)
    $sql = "INSERT INTO ProductionStages (StageName, Description, StageOrder, StageCost, StageTime) VALUES ('Kneading and Shaping', 'Knead dough and shape according to product', 2, 8.00, 45)"
    $connection.Execute($sql)
    $sql = "INSERT INTO ProductionStages (StageName, Description, StageOrder, StageCost, StageTime) VALUES ('Filling and Packaging', 'Fill maamoul or package pickles', 3, 10.00, 60)"
    $connection.Execute($sql)
    Write-Host "Production Stages data inserted" -ForegroundColor Green
    
    # Insert Cost Centers
    $sql = "INSERT INTO CostCenters (CenterName, CenterType, Description, IsActive) VALUES ('Maamoul Production Department', 'Production', 'Department specialized in producing all types of maamoul', True)"
    $connection.Execute($sql)
    $sql = "INSERT INTO CostCenters (CenterName, CenterType, Description, IsActive) VALUES ('Pickles Production Department', 'Production', 'Department specialized in producing and canning pickles', True)"
    $connection.Execute($sql)
    $sql = "INSERT INTO CostCenters (CenterName, CenterType, Description, IsActive) VALUES ('General Administration', 'Administrative', 'General administrative costs', True)"
    $connection.Execute($sql)
    Write-Host "Cost Centers data inserted" -ForegroundColor Green
    
    Write-Host "All test data inserted successfully!" -ForegroundColor Green
    
    $connection.Close()
}
catch {
    Write-Host "Error inserting data" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Yellow
}
