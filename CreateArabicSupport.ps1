# Create Arabic Support Tables
Write-Host "Creating Arabic Support Tables" -ForegroundColor Green

try {
    $connectionString = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=" + (Get-Location).Path + "\Factory_Accounting_System.accdb"
    $connection = New-Object -ComObject ADODB.Connection
    $connection.Open($connectionString)
    
    Write-Host "Database connection successful" -ForegroundColor Green
    
    # 1. Create System Settings table
    Write-Host "Creating System Settings table..." -ForegroundColor Yellow
    
    try {
        $sql = "CREATE TABLE SystemSettings (SettingID AUTOINCREMENT PRIMARY KEY, SettingName TEXT(100) NOT NULL, SettingValue TEXT(255), SettingDescription TEXT(500), SettingCategory TEXT(50), IsActive YESNO DEFAULT True, CreatedDate DATETIME DEFAULT Now(), ModifiedDate DATETIME)"
        $connection.Execute($sql)
        Write-Host "✓ SystemSettings table created" -ForegroundColor Green
    }
    catch {
        Write-Host "SystemSettings table already exists" -ForegroundColor Yellow
    }
    
    # 2. Insert basic settings
    Write-Host "Inserting basic settings..." -ForegroundColor Yellow
    
    $settings = @(
        "DefaultLanguage,Arabic,Default system language,Language",
        "DateFormat,dd/mm/yyyy,Date format,Format",
        "CurrencyFormat,SAR,Currency format,Format",
        "TextDirection,RTL,Text direction,Display",
        "FontName,Tahoma,Default font,Display",
        "FontSize,11,Default font size,Display"
    )
    
    foreach ($setting in $settings) {
        $parts = $setting.Split(',')
        try {
            $sql = "INSERT INTO SystemSettings (SettingName, SettingValue, SettingDescription, SettingCategory) VALUES ('" + $parts[0] + "', '" + $parts[1] + "', '" + $parts[2] + "', '" + $parts[3] + "')"
            $connection.Execute($sql)
        }
        catch {
            Write-Host "Setting already exists: " $parts[0] -ForegroundColor Yellow
        }
    }
    Write-Host "✓ Basic settings inserted" -ForegroundColor Green
    
    # 3. Create User Settings table
    Write-Host "Creating User Settings table..." -ForegroundColor Yellow
    
    try {
        $sql = "CREATE TABLE UserSettings (UserSettingID AUTOINCREMENT PRIMARY KEY, UserName TEXT(50) NOT NULL, LanguagePreference TEXT(20) DEFAULT 'Arabic', DateFormat TEXT(20) DEFAULT 'dd/mm/yyyy', FontName TEXT(50) DEFAULT 'Tahoma', FontSize INTEGER DEFAULT 11, IsActive YESNO DEFAULT True, CreatedDate DATETIME DEFAULT Now())"
        $connection.Execute($sql)
        Write-Host "✓ UserSettings table created" -ForegroundColor Green
        
        # Insert default user
        $sql = "INSERT INTO UserSettings (UserName, LanguagePreference, DateFormat, FontName, FontSize) VALUES ('Default', 'Arabic', 'dd/mm/yyyy', 'Tahoma', 11)"
        $connection.Execute($sql)
        Write-Host "✓ Default user settings inserted" -ForegroundColor Green
    }
    catch {
        Write-Host "UserSettings table already exists" -ForegroundColor Yellow
    }
    
    # 4. Create Activity Log table
    Write-Host "Creating Activity Log table..." -ForegroundColor Yellow
    
    try {
        $sql = "CREATE TABLE ActivityLog (LogID AUTOINCREMENT PRIMARY KEY, UserName TEXT(50), ActivityType TEXT(50), ActivityDescription TEXT(500), TableName TEXT(50), RecordID LONG, ActivityDate DATETIME DEFAULT Now())"
        $connection.Execute($sql)
        Write-Host "✓ ActivityLog table created" -ForegroundColor Green
    }
    catch {
        Write-Host "ActivityLog table already exists" -ForegroundColor Yellow
    }
    
    # 5. Test the configuration
    Write-Host "Testing configuration..." -ForegroundColor Yellow
    
    try {
        $recordset = New-Object -ComObject ADODB.Recordset
        $recordset.Open("SELECT COUNT(*) as SettingsCount FROM SystemSettings", $connection)
        
        if (-not $recordset.EOF) {
            $count = $recordset.Fields.Item(0).Value
            Write-Host "✓ Configuration test successful - $count settings found" -ForegroundColor Green
        }
        $recordset.Close()
    }
    catch {
        Write-Host "Configuration test failed" -ForegroundColor Red
        Write-Host $_.Exception.Message -ForegroundColor Yellow
    }
    
    Write-Host "`n=== Arabic Support Configuration Complete ===" -ForegroundColor Cyan
    Write-Host "✓ System Settings table created" -ForegroundColor Green
    Write-Host "✓ User Settings table created" -ForegroundColor Green
    Write-Host "✓ Activity Log table created" -ForegroundColor Green
    Write-Host "✓ Basic configuration inserted" -ForegroundColor Green
    
    Write-Host "`nNext Steps:" -ForegroundColor Yellow
    Write-Host "1. Open Access and set form properties to RightToLeft = True" -ForegroundColor White
    Write-Host "2. Use Tahoma font for Arabic text display" -ForegroundColor White
    Write-Host "3. Test Arabic text input in forms" -ForegroundColor White
    Write-Host "4. Configure reports for RTL layout" -ForegroundColor White
    
    $connection.Close()
}
catch {
    Write-Host "Error creating Arabic support tables" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Yellow
}
