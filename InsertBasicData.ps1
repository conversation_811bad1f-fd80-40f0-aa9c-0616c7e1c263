# PowerShell script to insert basic data
try {
    # Create Access application object
    $accessApp = New-Object -ComObject Access.Application
    $accessApp.Visible = $true
    
    # Open existing database
    $currentDir = Get-Location
    $dbPath = Join-Path $currentDir "Factory_Accounting_System.accdb"
    $accessApp.OpenCurrentDatabase($dbPath)
    
    Write-Host "Database opened successfully"
    
    # Insert Units data
    $sql = "INSERT INTO Units (UnitName, Symbol) VALUES ('كيلوجرام', 'كجم')"
    $accessApp.DoCmd.RunSQL($sql)
    
    $sql = "INSERT INTO Units (UnitName, Symbol) VALUES ('جرام', 'جم')"
    $accessApp.DoCmd.RunSQL($sql)
    
    $sql = "INSERT INTO Units (UnitName, Symbol) VALUES ('لتر', 'لتر')"
    $accessApp.DoCmd.RunSQL($sql)
    
    $sql = "INSERT INTO Units (UnitName, Symbol) VALUES ('قطعة', 'قطعة')"
    $accessApp.DoCmd.RunSQL($sql)
    
    $sql = "INSERT INTO Units (UnitName, Symbol) VALUES ('علبة', 'علبة')"
    $accessApp.DoCmd.RunSQL($sql)
    
    Write-Host "Units data inserted"
    
    # Insert Currencies data
    $sql = "INSERT INTO Currencies (CurrencyName, Symbol, ExchangeRate) VALUES ('الريال السعودي', 'ريال', 1.00)"
    $accessApp.DoCmd.RunSQL($sql)
    
    $sql = "INSERT INTO Currencies (CurrencyName, Symbol, ExchangeRate) VALUES ('الدولار الأمريكي', '$', 3.75)"
    $accessApp.DoCmd.RunSQL($sql)
    
    Write-Host "Currencies data inserted"
    
    # Insert Categories data
    $sql = "INSERT INTO Categories (CategoryName, Description, CategoryType) VALUES ('دقيق ونشويات', 'الدقيق والنشويات المستخدمة في التصنيع', 'مواد خام')"
    $accessApp.DoCmd.RunSQL($sql)
    
    $sql = "INSERT INTO Categories (CategoryName, Description, CategoryType) VALUES ('سكر ومحليات', 'السكر والمحليات الطبيعية والصناعية', 'مواد خام')"
    $accessApp.DoCmd.RunSQL($sql)
    
    $sql = "INSERT INTO Categories (CategoryName, Description, CategoryType) VALUES ('زيوت ودهون', 'الزيوت والدهون المستخدمة في الطبخ', 'مواد خام')"
    $accessApp.DoCmd.RunSQL($sql)
    
    $sql = "INSERT INTO Categories (CategoryName, Description, CategoryType) VALUES ('توابل وبهارات', 'التوابل والبهارات والنكهات', 'مواد خام')"
    $accessApp.DoCmd.RunSQL($sql)
    
    $sql = "INSERT INTO Categories (CategoryName, Description, CategoryType) VALUES ('خضروات وفواكه', 'الخضروات والفواكه الطازجة والمجففة', 'مواد خام')"
    $accessApp.DoCmd.RunSQL($sql)
    
    $sql = "INSERT INTO Categories (CategoryName, Description, CategoryType) VALUES ('مواد حافظة', 'المواد الحافظة والمثبتات', 'مواد خام')"
    $accessApp.DoCmd.RunSQL($sql)
    
    $sql = "INSERT INTO Categories (CategoryName, Description, CategoryType) VALUES ('عبوات وتغليف', 'العبوات ومواد التغليف', 'مواد خام')"
    $accessApp.DoCmd.RunSQL($sql)
    
    $sql = "INSERT INTO Categories (CategoryName, Description, CategoryType) VALUES ('معمول', 'أصناف المعمول المختلفة', 'منتجات نهائية')"
    $accessApp.DoCmd.RunSQL($sql)
    
    $sql = "INSERT INTO Categories (CategoryName, Description, CategoryType) VALUES ('مخللات', 'أصناف المخللات المختلفة', 'منتجات نهائية')"
    $accessApp.DoCmd.RunSQL($sql)
    
    Write-Host "Categories data inserted"
    
    # Insert ProductionStages data
    $sql = "INSERT INTO ProductionStages (StageName, Description, StageOrder, StageTime) VALUES ('التحضير والخلط', 'تحضير وخلط المكونات الأساسية', 1, 30)"
    $accessApp.DoCmd.RunSQL($sql)
    
    $sql = "INSERT INTO ProductionStages (StageName, Description, StageOrder, StageTime) VALUES ('العجن والتشكيل', 'عجن العجينة وتشكيلها', 2, 45)"
    $accessApp.DoCmd.RunSQL($sql)
    
    $sql = "INSERT INTO ProductionStages (StageName, Description, StageOrder, StageTime) VALUES ('الحشو والتعبئة', 'حشو المعمول أو تعبئة المخللات', 3, 60)"
    $accessApp.DoCmd.RunSQL($sql)
    
    $sql = "INSERT INTO ProductionStages (StageName, Description, StageOrder, StageTime) VALUES ('الطبخ والمعالجة', 'طبخ المعمول أو معالجة المخللات', 4, 90)"
    $accessApp.DoCmd.RunSQL($sql)
    
    $sql = "INSERT INTO ProductionStages (StageName, Description, StageOrder, StageTime) VALUES ('التبريد والتجفيف', 'تبريد المنتج وتجفيفه', 5, 120)"
    $accessApp.DoCmd.RunSQL($sql)
    
    $sql = "INSERT INTO ProductionStages (StageName, Description, StageOrder, StageTime) VALUES ('التغليف والتعبئة', 'تغليف المنتج النهائي', 6, 20)"
    $accessApp.DoCmd.RunSQL($sql)
    
    Write-Host "ProductionStages data inserted"
    
    # Insert CostCenters data
    $sql = "INSERT INTO CostCenters (CenterName, CenterType, Description) VALUES ('قسم إنتاج المعمول', 'إنتاج', 'قسم متخصص في إنتاج جميع أنواع المعمول')"
    $accessApp.DoCmd.RunSQL($sql)
    
    $sql = "INSERT INTO CostCenters (CenterName, CenterType, Description) VALUES ('قسم إنتاج المخللات', 'إنتاج', 'قسم متخصص في إنتاج وتعليب المخللات')"
    $accessApp.DoCmd.RunSQL($sql)
    
    $sql = "INSERT INTO CostCenters (CenterName, CenterType, Description) VALUES ('قسم التغليف والتعبئة', 'إنتاج', 'قسم التغليف النهائي للمنتجات')"
    $accessApp.DoCmd.RunSQL($sql)
    
    $sql = "INSERT INTO CostCenters (CenterName, CenterType, Description) VALUES ('قسم مراقبة الجودة', 'إنتاج', 'قسم فحص ومراقبة جودة المنتجات')"
    $accessApp.DoCmd.RunSQL($sql)
    
    $sql = "INSERT INTO CostCenters (CenterName, CenterType, Description) VALUES ('الإدارة العامة', 'إداري', 'التكاليف الإدارية العامة')"
    $accessApp.DoCmd.RunSQL($sql)
    
    Write-Host "CostCenters data inserted"
    
    Write-Host "All basic data inserted successfully!"
}
catch {
    Write-Host "Error: $($_.Exception.Message)"
}
