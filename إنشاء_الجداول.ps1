# سكريبت PowerShell لإنشاء جداول قاعدة البيانات
try {
    # إنشاء كائن Access
    $accessApp = New-Object -ComObject Access.Application
    $accessApp.Visible = $false
    
    # فتح قاعدة البيانات
    $dbPath = "D:\QQ@WORK1\الاجازات\23\نظام_محاسبة_المصنع.accdb"
    $accessApp.OpenCurrentDatabase($dbPath)
    $db = $accessApp.CurrentDb
    
    Write-Host "تم فتح قاعدة البيانات بنجاح"
    
    # إنشاء جدول وحدات القياس
    $sql = "CREATE TABLE وحدات_القياس (رقم_الوحدة AUTOINCREMENT PRIMARY KEY, اسم_الوحدة TEXT(50) NOT NULL, الرمز TEXT(10), ملاحظات MEMO)"
    $db.Execute($sql)
    Write-Host "تم إنشاء جدول وحدات القياس"
    
    # إنشاء جدول العملات
    $sql = "CREATE TABLE العملات (رقم_العملة AUTOINCREMENT PRIMARY KEY, اسم_العملة TEXT(50) NOT NULL, الرمز TEXT(10), سعر_الصرف CURRENCY DEFAULT 1, تاريخ_التحديث DATETIME DEFAULT Now())"
    $db.Execute($sql)
    Write-Host "تم إنشاء جدول العملات"
    
    # إنشاء جدول فئات المواد
    $sql = "CREATE TABLE فئات_المواد (رقم_الفئة AUTOINCREMENT PRIMARY KEY, اسم_الفئة TEXT(100) NOT NULL, وصف_الفئة MEMO, نوع_الفئة TEXT(20))"
    $db.Execute($sql)
    Write-Host "تم إنشاء جدول فئات المواد"
    
    # إنشاء جدول المواد الخام
    $sql = "CREATE TABLE المواد_الخام (رقم_المادة AUTOINCREMENT PRIMARY KEY, اسم_المادة TEXT(100) NOT NULL, رقم_الفئة LONG, رقم_الوحدة LONG, الحد_الأدنى DOUBLE DEFAULT 0, متوسط_السعر CURRENCY DEFAULT 0, ملاحظات MEMO, تاريخ_الإنشاء DATETIME DEFAULT Now(), نشط YESNO DEFAULT True)"
    $db.Execute($sql)
    Write-Host "تم إنشاء جدول المواد الخام"
    
    # إنشاء جدول الموردين
    $sql = "CREATE TABLE الموردين (رقم_المورد AUTOINCREMENT PRIMARY KEY, اسم_المورد TEXT(100) NOT NULL, اسم_جهة_الاتصال TEXT(100), الهاتف TEXT(20), الجوال TEXT(20), البريد_الإلكتروني TEXT(100), العنوان MEMO, المدينة TEXT(50), الدولة TEXT(50), الرقم_الضريبي TEXT(50), شروط_الدفع TEXT(100), مدة_الائتمان INTEGER DEFAULT 0, حد_الائتمان CURRENCY DEFAULT 0, رصيد_الحساب CURRENCY DEFAULT 0, تاريخ_التسجيل DATETIME DEFAULT Now(), نشط YESNO DEFAULT True, ملاحظات MEMO)"
    $db.Execute($sql)
    Write-Host "تم إنشاء جدول الموردين"
    
    # إنشاء جدول فواتير الشراء
    $sql = "CREATE TABLE فواتير_الشراء (رقم_الفاتورة AUTOINCREMENT PRIMARY KEY, رقم_فاتورة_المورد TEXT(50), رقم_المورد LONG NOT NULL, تاريخ_الفاتورة DATETIME DEFAULT Now(), تاريخ_الاستحقاق DATETIME, رقم_العملة LONG DEFAULT 1, سعر_الصرف CURRENCY DEFAULT 1, إجمالي_قبل_الضريبة CURRENCY DEFAULT 0, قيمة_الضريبة CURRENCY DEFAULT 0, نسبة_الضريبة DOUBLE DEFAULT 0, إجمالي_الفاتورة CURRENCY DEFAULT 0, المبلغ_المدفوع CURRENCY DEFAULT 0, المبلغ_المتبقي CURRENCY DEFAULT 0, حالة_الفاتورة TEXT(20) DEFAULT 'مفتوحة', ملاحظات MEMO, تاريخ_الإدخال DATETIME DEFAULT Now(), مستخدم_الإدخال TEXT(50))"
    $db.Execute($sql)
    Write-Host "تم إنشاء جدول فواتير الشراء"
    
    # إنشاء جدول تفاصيل فواتير الشراء
    $sql = "CREATE TABLE تفاصيل_فواتير_الشراء (رقم_التفصيل AUTOINCREMENT PRIMARY KEY, رقم_الفاتورة LONG NOT NULL, رقم_المادة LONG NOT NULL, الكمية DOUBLE NOT NULL, سعر_الوحدة CURRENCY NOT NULL, إجمالي_السطر CURRENCY, نسبة_الخصم DOUBLE DEFAULT 0, قيمة_الخصم CURRENCY DEFAULT 0, الصافي CURRENCY, تاريخ_الانتهاء DATETIME, رقم_اللوط TEXT(50), ملاحظات MEMO)"
    $db.Execute($sql)
    Write-Host "تم إنشاء جدول تفاصيل فواتير الشراء"
    
    # إنشاء جدول المنتجات
    $sql = "CREATE TABLE المنتجات (رقم_المنتج AUTOINCREMENT PRIMARY KEY, اسم_المنتج TEXT(100) NOT NULL, رقم_الفئة LONG, رقم_الوحدة LONG, سعر_البيع CURRENCY DEFAULT 0, تكلفة_الإنتاج CURRENCY DEFAULT 0, وقت_الإنتاج INTEGER DEFAULT 0, الحد_الأدنى DOUBLE DEFAULT 0, وصف_المنتج MEMO, تاريخ_الإنشاء DATETIME DEFAULT Now(), نشط YESNO DEFAULT True)"
    $db.Execute($sql)
    Write-Host "تم إنشاء جدول المنتجات"
    
    # إنشاء جدول وصفات الإنتاج
    $sql = "CREATE TABLE وصفات_الإنتاج (رقم_الوصفة AUTOINCREMENT PRIMARY KEY, رقم_المنتج LONG NOT NULL, رقم_المادة LONG NOT NULL, الكمية_المطلوبة DOUBLE NOT NULL, تكلفة_المادة CURRENCY DEFAULT 0, ملاحظات MEMO)"
    $db.Execute($sql)
    Write-Host "تم إنشاء جدول وصفات الإنتاج"
    
    # إنشاء جدول مراحل التصنيع
    $sql = "CREATE TABLE مراحل_التصنيع (رقم_المرحلة AUTOINCREMENT PRIMARY KEY, اسم_المرحلة TEXT(100) NOT NULL, وصف_المرحلة MEMO, ترتيب_المرحلة INTEGER, تكلفة_المرحلة CURRENCY DEFAULT 0, وقت_المرحلة INTEGER DEFAULT 0)"
    $db.Execute($sql)
    Write-Host "تم إنشاء جدول مراحل التصنيع"
    
    # إنشاء جدول مراحل المنتجات
    $sql = "CREATE TABLE مراحل_المنتجات (رقم_السجل AUTOINCREMENT PRIMARY KEY, رقم_المنتج LONG NOT NULL, رقم_المرحلة LONG NOT NULL, ترتيب_المرحلة INTEGER, ملاحظات MEMO)"
    $db.Execute($sql)
    Write-Host "تم إنشاء جدول مراحل المنتجات"
    
    # إنشاء جدول أوامر الإنتاج
    $sql = "CREATE TABLE أوامر_الإنتاج (رقم_الأمر AUTOINCREMENT PRIMARY KEY, رقم_المنتج LONG NOT NULL, الكمية_المطلوبة DOUBLE NOT NULL, الكمية_المنتجة DOUBLE DEFAULT 0, تاريخ_الأمر DATETIME DEFAULT Now(), تاريخ_البدء DATETIME, تاريخ_الانتهاء DATETIME, حالة_الأمر TEXT(20) DEFAULT 'جديد', التكلفة_المقدرة CURRENCY DEFAULT 0, التكلفة_الفعلية CURRENCY DEFAULT 0, ملاحظات MEMO, مستخدم_الإدخال TEXT(50))"
    $db.Execute($sql)
    Write-Host "تم إنشاء جدول أوامر الإنتاج"
    
    Write-Host "تم إنشاء جميع الجداول الأساسية بنجاح!"
    
    # إغلاق قاعدة البيانات
    $accessApp.CloseCurrentDatabase()
    $accessApp.Quit()
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($accessApp) | Out-Null
}
catch {
    Write-Host "خطأ في إنشاء الجداول: $($_.Exception.Message)"
}
