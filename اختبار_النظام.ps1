# سكريبت اختبار شامل لنظام محاسبة المصنع
Write-Host "=== بدء اختبار نظام محاسبة مصنع المعمول والمخللات ===" -ForegroundColor Green

# 1. اختبار وجود الملفات الأساسية
Write-Host "`n1. اختبار وجود الملفات الأساسية:" -ForegroundColor Yellow

$requiredFiles = @(
    "Factory_Accounting_System.accdb",
    "بيانات_تجريبية.sql",
    "الاستعلامات_والحسابات.sql",
    "إنشاء_النماذج.txt",
    "الحسابات_التلقائية.txt",
    "دليل_الاستخدام.md"
)

foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "✓ $file موجود" -ForegroundColor Green
    } else {
        Write-Host "✗ $file مفقود" -ForegroundColor Red
    }
}

# 2. اختبار قاعدة البيانات
Write-Host "`n2. اختبار قاعدة البيانات:" -ForegroundColor Yellow

try {
    # إنشاء اتصال ODBC لقاعدة البيانات
    $connectionString = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=$((Get-Location).Path)\Factory_Accounting_System.accdb"
    $connection = New-Object -ComObject ADODB.Connection
    $connection.Open($connectionString)
    
    Write-Host "✓ تم الاتصال بقاعدة البيانات بنجاح" -ForegroundColor Green
    
    # قائمة الجداول المطلوبة
    $expectedTables = @(
        "Units", "Currencies", "Categories", "RawMaterials", "Suppliers",
        "PurchaseInvoices", "PurchaseInvoiceDetails", "Products", "ProductRecipes",
        "ProductionStages", "ProductionOrders", "RawMaterialsInventory",
        "FinishedProductsInventory", "InventoryMovements", "CostCenters", "ProductionCosts"
    )
    
    # التحقق من وجود الجداول
    $recordset = New-Object -ComObject ADODB.Recordset
    $recordset.Open("SELECT Name FROM MSysObjects WHERE Type=1 AND Flags=0", $connection)
    
    $existingTables = @()
    while (-not $recordset.EOF) {
        $existingTables += $recordset.Fields.Item("Name").Value
        $recordset.MoveNext()
    }
    $recordset.Close()
    
    Write-Host "`nالجداول الموجودة:"
    foreach ($table in $expectedTables) {
        if ($existingTables -contains $table) {
            Write-Host "✓ $table" -ForegroundColor Green
        } else {
            Write-Host "✗ $table مفقود" -ForegroundColor Red
        }
    }
    
    $connection.Close()
}
catch {
    Write-Host "✗ خطأ في الاتصال بقاعدة البيانات: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. اختبار إدراج البيانات الأساسية
Write-Host "`n3. اختبار إدراج البيانات الأساسية:" -ForegroundColor Yellow

try {
    $connection = New-Object -ComObject ADODB.Connection
    $connection.Open($connectionString)
    
    # اختبار إدراج وحدة قياس
    $command = New-Object -ComObject ADODB.Command
    $command.ActiveConnection = $connection
    $command.CommandText = "INSERT INTO Units (UnitName, Symbol) VALUES ('كيلوجرام تجريبي', 'كجم ت')"
    $command.Execute() | Out-Null
    Write-Host "✓ تم إدراج بيانات تجريبية في جدول Units" -ForegroundColor Green
    
    # اختبار إدراج عملة
    $command.CommandText = "INSERT INTO Currencies (CurrencyName, Symbol, ExchangeRate) VALUES ('عملة تجريبية', 'تج', 1.0)"
    $command.Execute() | Out-Null
    Write-Host "✓ تم إدراج بيانات تجريبية في جدول Currencies" -ForegroundColor Green
    
    # اختبار إدراج فئة
    $command.CommandText = "INSERT INTO Categories (CategoryName, Description, CategoryType) VALUES ('فئة تجريبية', 'وصف تجريبي', 'مواد خام')"
    $command.Execute() | Out-Null
    Write-Host "✓ تم إدراج بيانات تجريبية في جدول Categories" -ForegroundColor Green
    
    $connection.Close()
}
catch {
    Write-Host "✗ خطأ في إدراج البيانات: $($_.Exception.Message)" -ForegroundColor Red
}

# 4. اختبار الاستعلامات
Write-Host "`n4. اختبار الاستعلامات الأساسية:" -ForegroundColor Yellow

try {
    $connection = New-Object -ComObject ADODB.Connection
    $connection.Open($connectionString)
    
    # اختبار استعلام عدد السجلات في كل جدول
    $testQueries = @{
        "Units" = "SELECT COUNT(*) as RecordCount FROM Units"
        "Currencies" = "SELECT COUNT(*) as RecordCount FROM Currencies"
        "Categories" = "SELECT COUNT(*) as RecordCount FROM Categories"
        "Suppliers" = "SELECT COUNT(*) as RecordCount FROM Suppliers"
    }
    
    foreach ($table in $testQueries.Keys) {
        try {
            $recordset = New-Object -ComObject ADODB.Recordset
            $recordset.Open($testQueries[$table], $connection)
            $count = $recordset.Fields.Item("RecordCount").Value
            $recordset.Close()
            Write-Host "✓ جدول $table يحتوي على $count سجل" -ForegroundColor Green
        }
        catch {
            Write-Host "✗ خطأ في استعلام جدول $table" -ForegroundColor Red
        }
    }
    
    $connection.Close()
}
catch {
    Write-Host "✗ خطأ في اختبار الاستعلامات: $($_.Exception.Message)" -ForegroundColor Red
}

# 5. اختبار العلاقات بين الجداول
Write-Host "`n5. اختبار العلاقات بين الجداول:" -ForegroundColor Yellow

try {
    $connection = New-Object -ComObject ADODB.Connection
    $connection.Open($connectionString)
    
    # اختبار العلاقة بين RawMaterials و Categories
    $query = "SELECT rm.MaterialName, c.CategoryName FROM RawMaterials rm LEFT JOIN Categories c ON rm.CategoryID = c.CategoryID"
    $recordset = New-Object -ComObject ADODB.Recordset
    $recordset.Open($query, $connection)
    
    if (-not $recordset.EOF) {
        Write-Host "✓ العلاقة بين RawMaterials و Categories تعمل بشكل صحيح" -ForegroundColor Green
    } else {
        Write-Host "! لا توجد بيانات لاختبار العلاقة" -ForegroundColor Yellow
    }
    $recordset.Close()
    
    $connection.Close()
}
catch {
    Write-Host "✗ خطأ في اختبار العلاقات: $($_.Exception.Message)" -ForegroundColor Red
}

# 6. تقرير النتائج النهائية
Write-Host "`n=== تقرير الاختبار النهائي ===" -ForegroundColor Cyan
Write-Host "تم إجراء اختبار شامل للنظام" -ForegroundColor White
Write-Host "للحصول على تفاصيل أكثر، راجع الرسائل أعلاه" -ForegroundColor White

Write-Host "`nانتهى الاختبار" -ForegroundColor Green
