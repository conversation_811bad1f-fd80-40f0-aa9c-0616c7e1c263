# دليل استخدام نظام محاسبة مصنع المعمول والمخللات

## نظرة عامة على النظام

تم تصميم هذا النظام المحاسبي الشامل خصيصاً لمصانع المعمول والمخللات باستخدام Microsoft Access مع واجهة عربية كاملة. يوفر النظام إدارة متكاملة للمشتريات والتصنيع والمخزون مع حسابات تلقائية للتكاليف والأرباح.

## مكونات النظام

### 1. الجداول الأساسية

#### جداول إدارة المشتريات:
- **Units**: وحدات القياس (كجم، جم، لتر، قطعة، إلخ)
- **Currencies**: العملات المستخدمة
- **Categories**: فئات المواد والمنتجات
- **RawMaterials**: المواد الخام
- **Suppliers**: بيانات الموردين
- **PurchaseInvoices**: فواتير الشراء
- **PurchaseInvoiceDetails**: تفاصيل فواتير الشراء

#### جداول إدارة التصنيع:
- **Products**: المنتجات النهائية
- **ProductRecipes**: وصفات الإنتاج (المواد المطلوبة لكل منتج)
- **ProductionStages**: مراحل التصنيع
- **ProductionOrders**: أوامر الإنتاج
- **CostCenters**: مراكز التكلفة
- **ProductionCosts**: تكاليف الإنتاج

#### جداول إدارة المخزون:
- **RawMaterialsInventory**: مخزون المواد الخام
- **FinishedProductsInventory**: مخزون المنتجات النهائية
- **InventoryMovements**: حركات المخزون

### 2. الاستعلامات والتقارير

#### الاستعلامات الأساسية:
- **تقرير_المخزون_الشامل**: عرض شامل لحالة المخزون
- **تقرير_تكاليف_الإنتاج_التفصيلي**: تحليل تكاليف الإنتاج
- **الفواتير_المستحقة_والمتأخرة**: متابعة المدفوعات
- **تحليل_الأرباح_والمبيعات**: تحليل الربحية
- **حركات_المخزون_التفصيلية**: تتبع حركات المخزون
- **تقرير_الموردين_والأرصدة**: حالة الموردين
- **تقرير_استهلاك_المواد_الخام**: تحليل الاستهلاك
- **تقرير_كفاءة_الإنتاج**: قياس الأداء

## خطوات التشغيل الأولى

### 1. إعداد البيانات الأساسية

#### أ. إدخال وحدات القياس:
```
- كيلوجرام (كجم)
- جرام (جم)
- لتر (لتر)
- قطعة (قطعة)
- علبة (علبة)
```

#### ب. إدخال العملات:
```
- الريال السعودي (1.00)
- الدولار الأمريكي (3.75)
- اليورو (4.10)
```

#### ج. إدخال فئات المواد:
```
مواد خام:
- دقيق ونشويات
- سكر ومحليات
- زيوت ودهون
- توابل وبهارات
- خضروات وفواكه
- مواد حافظة
- عبوات وتغليف

منتجات نهائية:
- معمول
- مخللات
```

### 2. إدخال بيانات الموردين

في جدول **Suppliers**، أدخل:
- اسم المورد
- بيانات الاتصال (هاتف، جوال، بريد إلكتروني)
- العنوان والمدينة
- الرقم الضريبي
- شروط الدفع
- حد الائتمان

### 3. إدخال المواد الخام

في جدول **RawMaterials**، أدخل:
- اسم المادة
- الفئة
- وحدة القياس
- الحد الأدنى للمخزون
- متوسط السعر

### 4. إدخال المنتجات النهائية

في جدول **Products**، أدخل:
- اسم المنتج
- الفئة
- وحدة القياس
- سعر البيع
- تكلفة الإنتاج المقدرة
- وقت الإنتاج
- الحد الأدنى للمخزون

## العمليات اليومية

### 1. إدارة المشتريات

#### أ. إدخال فاتورة شراء:
1. افتح جدول **PurchaseInvoices**
2. أدخل بيانات الفاتورة:
   - رقم فاتورة المورد
   - المورد
   - تاريخ الفاتورة
   - تاريخ الاستحقاق
   - العملة وسعر الصرف

3. في جدول **PurchaseInvoiceDetails**، أدخل:
   - المادة الخام
   - الكمية
   - سعر الوحدة
   - نسبة الخصم (إن وجد)

#### ب. تحديث المخزون تلقائياً:
استخدم الكود VBA التالي:
```vba
Call UpdateRawMaterialInventoryPurchase(MaterialID, Quantity, UnitPrice, InvoiceID)
```

### 2. إدارة الإنتاج

#### أ. إنشاء أمر إنتاج:
1. افتح جدول **ProductionOrders**
2. أدخل:
   - المنتج
   - الكمية المطلوبة
   - تاريخ البدء المتوقع
   - التكلفة المقدرة

#### ب. تسجيل استهلاك المواد:
استخدم الكود VBA:
```vba
Call UpdateRawMaterialInventoryConsumption(MaterialID, ConsumedQuantity, ProductionOrderID)
```

#### ج. إضافة المنتج النهائي للمخزون:
```vba
Call AddFinishedProductToInventory(ProductID, ProducedQuantity, ProductionCost, ProductionOrderID)
```

### 3. المتابعة والتقارير

#### أ. مراقبة المخزون:
- تشغيل استعلام **تقرير_المخزون_الشامل**
- فحص المواد تحت الحد الأدنى
- استخدام: `Call CheckMinimumInventoryLevels()`

#### ب. تحليل التكاليف:
- تشغيل استعلام **تقرير_تكاليف_الإنتاج_التفصيلي**
- مقارنة التكاليف المقدرة بالفعلية
- تحليل انحرافات التكلفة

#### ج. متابعة المدفوعات:
- تشغيل استعلام **الفواتير_المستحقة_والمتأخرة**
- متابعة أرصدة الموردين

## الميزات المتقدمة

### 1. الحسابات التلقائية

النظام يقوم تلقائياً بـ:
- تحديث أرصدة المخزون عند الشراء والاستهلاك
- حساب متوسط تكلفة المواد
- حساب تكاليف الإنتاج الفعلية
- تحديث أرصدة الموردين
- التحقق من الحد الأدنى للمخزون

### 2. التقارير التحليلية

- تحليل الربحية لكل منتج
- كفاءة الإنتاج ومقارنة الأداء
- تحليل استهلاك المواد الخام
- تقارير حركات المخزون التفصيلية

### 3. التنبيهات والتحذيرات

- تنبيهات المخزون تحت الحد الأدنى
- تحذيرات الفواتير المتأخرة
- تنبيهات تجاوز حد الائتمان للموردين

## نصائح للاستخدام الأمثل

1. **النسخ الاحتياطي**: قم بعمل نسخة احتياطية يومية من قاعدة البيانات
2. **التحديث المنتظم**: حدث أسعار المواد الخام بانتظام
3. **مراجعة التقارير**: راجع التقارير المالية شهرياً
4. **تدريب المستخدمين**: تأكد من تدريب جميع المستخدمين على النظام
5. **الصيانة الدورية**: قم بضغط قاعدة البيانات دورياً لتحسين الأداء

## الدعم الفني

للحصول على الدعم الفني أو تطوير ميزات إضافية، يمكن التواصل مع فريق التطوير.

---

**ملاحظة**: هذا النظام مصمم خصيصاً لمصانع المعمول والمخللات ويمكن تخصيصه حسب احتياجات كل مصنع.
