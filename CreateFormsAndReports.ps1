# Create Forms and Reports in Access Database
Write-Host "Creating Forms and Reports in Access Database" -ForegroundColor Green

try {
    # Create Access Application object
    $accessApp = New-Object -ComObject Access.Application
    $accessApp.Visible = $true
    
    # Open the database
    $dbPath = (Get-Location).Path + "\Factory_Accounting_System.accdb"
    $accessApp.OpenCurrentDatabase($dbPath)
    
    Write-Host "Database opened successfully" -ForegroundColor Green
    
    # Create Suppliers Query
    Write-Host "Creating Suppliers Query..." -ForegroundColor Yellow
    
    $sql = @"
SELECT 
    s.SupplierName AS [اسم المورد],
    s.ContactPerson AS [جهة الاتصال],
    s.Phone AS [الهاتف],
    s.Email AS [البريد الإلكتروني],
    s.City AS [المدينة],
    s.CreditLimit AS [حد الائتمان],
    s.AccountBalance AS [رصيد الحساب],
    IIF(s.IsActive, "نشط", "غير نشط") AS [الحالة]
FROM Suppliers s
ORDER BY s.SupplierName
"@
    
    # Create the query
    $accessApp.DoCmd.RunSQL("CREATE VIEW استعلام_الموردين_العربي AS " + $sql)
    Write-Host "✓ Suppliers query created" -ForegroundColor Green
    
    # Create Raw Materials Query
    Write-Host "Creating Raw Materials Query..." -ForegroundColor Yellow
    
    $sql = @"
SELECT 
    rm.MaterialName AS [اسم المادة],
    c.CategoryName AS [الفئة],
    u.UnitName AS [وحدة القياس],
    rm.MinimumLevel AS [الحد الأدنى],
    rm.AveragePrice AS [متوسط السعر],
    IIF(rm.IsActive, "نشط", "غير نشط") AS [الحالة]
FROM (RawMaterials rm 
LEFT JOIN Categories c ON rm.CategoryID = c.CategoryID) 
LEFT JOIN Units u ON rm.UnitID = u.UnitID
ORDER BY c.CategoryName, rm.MaterialName
"@
    
    $accessApp.DoCmd.RunSQL("CREATE VIEW استعلام_المواد_الخام_العربي AS " + $sql)
    Write-Host "✓ Raw Materials query created" -ForegroundColor Green
    
    # Create Suppliers Form
    Write-Host "Creating Suppliers Form..." -ForegroundColor Yellow
    $accessApp.DoCmd.OpenForm("Suppliers", 1)  # Open in Design View
    $accessApp.DoCmd.Save(2, "نموذج_الموردين")  # Save as Form
    $accessApp.DoCmd.Close(2, "Suppliers")
    Write-Host "✓ Suppliers form created" -ForegroundColor Green
    
    # Create Raw Materials Form
    Write-Host "Creating Raw Materials Form..." -ForegroundColor Yellow
    $accessApp.DoCmd.OpenForm("RawMaterials", 1)  # Open in Design View
    $accessApp.DoCmd.Save(2, "نموذج_المواد_الخام")  # Save as Form
    $accessApp.DoCmd.Close(2, "RawMaterials")
    Write-Host "✓ Raw Materials form created" -ForegroundColor Green
    
    # Create Suppliers Report
    Write-Host "Creating Suppliers Report..." -ForegroundColor Yellow
    $accessApp.DoCmd.OpenReport("استعلام_الموردين_العربي", 1)  # Open in Design View
    $accessApp.DoCmd.Save(3, "تقرير_الموردين")  # Save as Report
    $accessApp.DoCmd.Close(3, "استعلام_الموردين_العربي")
    Write-Host "✓ Suppliers report created" -ForegroundColor Green
    
    # Create Raw Materials Report
    Write-Host "Creating Raw Materials Report..." -ForegroundColor Yellow
    $accessApp.DoCmd.OpenReport("استعلام_المواد_الخام_العربي", 1)  # Open in Design View
    $accessApp.DoCmd.Save(3, "تقرير_المواد_الخام")  # Save as Report
    $accessApp.DoCmd.Close(3, "استعلام_المواد_الخام_العربي")
    Write-Host "✓ Raw Materials report created" -ForegroundColor Green
    
    Write-Host "`n=== Forms and Reports Created Successfully! ===" -ForegroundColor Cyan
    Write-Host "✓ استعلام_الموردين_العربي - Suppliers Query" -ForegroundColor Green
    Write-Host "✓ استعلام_المواد_الخام_العربي - Raw Materials Query" -ForegroundColor Green
    Write-Host "✓ نموذج_الموردين - Suppliers Form" -ForegroundColor Green
    Write-Host "✓ نموذج_المواد_الخام - Raw Materials Form" -ForegroundColor Green
    Write-Host "✓ تقرير_الموردين - Suppliers Report" -ForegroundColor Green
    Write-Host "✓ تقرير_المواد_الخام - Raw Materials Report" -ForegroundColor Green
    
    Write-Host "`nNext Steps:" -ForegroundColor Yellow
    Write-Host "1. Refresh the Navigation Pane in Access (F5)" -ForegroundColor White
    Write-Host "2. You should now see Forms and Reports sections" -ForegroundColor White
    Write-Host "3. Double-click on any form or report to open it" -ForegroundColor White
    Write-Host "4. Customize the Arabic labels manually in Design View" -ForegroundColor White
    
    # Keep Access open for user to see the results
    Write-Host "`nAccess will remain open for you to see the new forms and reports." -ForegroundColor Cyan
    
}
catch {
    Write-Host "Error creating forms and reports" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Yellow
    
    if ($accessApp) {
        $accessApp.Quit()
    }
}
