' Create Forms and Reports in Access
Dim accessApp
Set accessApp = CreateObject("Access.Application")

' Make Access visible
accessApp.Visible = True

' Open the database
Dim dbPath
dbPath = CreateObject("Scripting.FileSystemObject").GetAbsolutePathName("Factory_Accounting_System.accdb")
accessApp.OpenCurrentDatabase dbPath

WScript.Echo "Database opened successfully"

' Create Suppliers Form
On Error Resume Next
accessApp.DoCmd.DeleteObject 2, "SuppliersForm" ' Delete if exists
On Error GoTo 0

' Create form using AutoForm
accessApp.DoCmd.OpenTable "Suppliers", 0 ' Open table
accessApp.DoCmd.RunCommand 2065 ' AutoForm command
accessApp.DoCmd.Save 2, "SuppliersForm"
accessApp.DoCmd.Close 2, "Suppliers"
WScript.Echo "Suppliers Form created"

' Create Raw Materials Form
On Error Resume Next
accessApp.DoCmd.DeleteObject 2, "RawMaterialsForm"
On Error GoTo 0

accessApp.DoCmd.OpenTable "RawMaterials", 0
accessApp.DoCmd.RunCommand 2065
accessApp.DoCmd.Save 2, "RawMaterialsForm"
accessApp.DoCmd.Close 2, "RawMaterials"
WScript.Echo "Raw Materials Form created"

' Create Purchase Invoices Form
On Error Resume Next
accessApp.DoCmd.DeleteObject 2, "PurchaseInvoicesForm"
On Error GoTo 0

accessApp.DoCmd.OpenTable "PurchaseInvoices", 0
accessApp.DoCmd.RunCommand 2065
accessApp.DoCmd.Save 2, "PurchaseInvoicesForm"
accessApp.DoCmd.Close 2, "PurchaseInvoices"
WScript.Echo "Purchase Invoices Form created"

' Create Suppliers Report
On Error Resume Next
accessApp.DoCmd.DeleteObject 3, "SuppliersReport"
On Error GoTo 0

accessApp.DoCmd.OpenTable "Suppliers", 0
accessApp.DoCmd.RunCommand 2067 ' AutoReport command
accessApp.DoCmd.Save 3, "SuppliersReport"
accessApp.DoCmd.Close 3, "Suppliers"
WScript.Echo "Suppliers Report created"

' Create Inventory Report
On Error Resume Next
accessApp.DoCmd.DeleteObject 3, "InventoryReport"
On Error GoTo 0

accessApp.DoCmd.OpenTable "RawMaterials", 0
accessApp.DoCmd.RunCommand 2067
accessApp.DoCmd.Save 3, "InventoryReport"
accessApp.DoCmd.Close 3, "RawMaterials"
WScript.Echo "Inventory Report created"

' Create Navigation Form
On Error Resume Next
accessApp.DoCmd.DeleteObject 2, "MainNavigationForm"
On Error GoTo 0

' Create blank form
accessApp.DoCmd.NewObjectType = 2 ' Form
accessApp.DoCmd.RunCommand 2065
accessApp.DoCmd.Save 2, "MainNavigationForm"
WScript.Echo "Main Navigation Form created"

WScript.Echo "All forms and reports created successfully!"
WScript.Echo "Check the Navigation Pane in Access to see:"
WScript.Echo "Forms: SuppliersForm, RawMaterialsForm, PurchaseInvoicesForm, MainNavigationForm"
WScript.Echo "Reports: SuppliersReport, InventoryReport"

' Keep Access open
WScript.Echo "Access will remain open for you to customize the forms and reports"
