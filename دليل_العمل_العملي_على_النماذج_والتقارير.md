# دليل العمل العملي على النماذج والتقارير

## 🎯 الخطوة 1: إنشاء النماذج العربية

### أ. إنشاء نموذج الموردين العربي

#### 1. إنشاء النموذج الأساسي:
1. **افتح Access** وتأكد من فتح قاعدة البيانات
2. **انتقل إلى Create Tab**
3. **اختر Form Wizard**
4. **اختر جدول Suppliers**
5. **اختر جميع الحقول** (>>)
6. **اختر Columnar Layout**
7. **اختر أي Style** (يمكن تغييره لاحقاً)
8. **اكتب اسم النموذج**: `نموذج_الموردين`

#### 2. تخصيص النموذج للعربية:
1. **انتقل إلى Design View** (انقر بالزر الأيمن على النموذج > Design View)
2. **اضبط خصائص النموذج**:
   - انقر بالزر الأيمن على النموذج > Properties
   - **Caption**: `إدارة الموردين - مصنع المعمول والمخللات`
   - **Right To Left**: `Yes`
   - **Right To Left Layout**: `Yes`

#### 3. تغيير تسميات الحقول للعربية:
انقر على كل Label وغير النص:
- `Supplier Name` → `اسم المورد`
- `Contact Person` → `جهة الاتصال`
- `Phone` → `الهاتف`
- `Mobile` → `الجوال`
- `Email` → `البريد الإلكتروني`
- `City` → `المدينة`
- `Country` → `الدولة`
- `Address` → `العنوان`
- `Payment Terms` → `شروط الدفع`
- `Credit Limit` → `حد الائتمان`
- `Account Balance` → `رصيد الحساب`
- `Notes` → `ملاحظات`

#### 4. ضبط الخطوط:
1. **اختر جميع العناصر** (Ctrl+A)
2. **في Properties**:
   - **Font Name**: `Tahoma`
   - **Font Size**: `11`
   - **Text Align**: `Right` (للنصوص العربية)

#### 5. إضافة أزرار التحكم:
1. **في Design View**، اختر **Button** من Toolbox
2. **أضف الأزرار التالية**:

**زر جديد**:
- **Caption**: `سجل جديد`
- **Name**: `btnNew`
- **On Click**: `[Event Procedure]`
- **الكود**:
```vba
Private Sub btnNew_Click()
    DoCmd.GoToRecord , , acNewRec
End Sub
```

**زر حفظ**:
- **Caption**: `حفظ`
- **Name**: `btnSave`
- **On Click**: `[Event Procedure]`
- **الكود**:
```vba
Private Sub btnSave_Click()
    If Me.Dirty Then
        DoCmd.RunCommand acCmdSaveRecord
        MsgBox "تم حفظ البيانات بنجاح", vbInformation, "حفظ"
    End If
End Sub
```

**زر حذف**:
- **Caption**: `حذف`
- **Name**: `btnDelete`
- **On Click**: `[Event Procedure]`
- **الكود**:
```vba
Private Sub btnDelete_Click()
    If MsgBox("هل أنت متأكد من حذف هذا السجل؟", vbYesNo + vbQuestion, "تأكيد الحذف") = vbYes Then
        DoCmd.RunCommand acCmdDeleteRecord
    End If
End Sub
```

**زر إغلاق**:
- **Caption**: `إغلاق`
- **Name**: `btnClose`
- **On Click**: `[Event Procedure]`
- **الكود**:
```vba
Private Sub btnClose_Click()
    DoCmd.Close acForm, Me.Name
End Sub
```

### ب. إنشاء نموذج المواد الخام

#### اتبع نفس الخطوات السابقة مع:
- **الجدول**: `RawMaterials`
- **اسم النموذج**: `نموذج_المواد_الخام`
- **التسميات العربية**:
  - `Material Name` → `اسم المادة`
  - `Category` → `الفئة`
  - `Unit` → `وحدة القياس`
  - `Minimum Level` → `الحد الأدنى`
  - `Average Price` → `متوسط السعر`

### ج. إنشاء النموذج الرئيسي للتنقل

#### 1. إنشاء نموذج فارغ:
1. **Create** > **Blank Form**
2. **احفظ باسم**: `القائمة_الرئيسية`

#### 2. ضبط خصائص النموذج:
- **Caption**: `نظام محاسبة مصنع المعمول والمخللات`
- **Default View**: `Single Form`
- **Allow Additions**: `No`
- **Allow Deletions**: `No`
- **Allow Edits**: `No`
- **Navigation Buttons**: `No`
- **Record Selectors**: `No`
- **Right To Left**: `Yes`
- **Right To Left Layout**: `Yes`

#### 3. إضافة العناصر:
1. **أضف Label للعنوان**:
   - **Caption**: `نظام محاسبة مصنع المعمول والمخللات`
   - **Font Size**: `18`
   - **Font Bold**: `Yes`
   - **Text Align**: `Center`
   - **Back Color**: أزرق فاتح

2. **أضف أزرار التنقل**:

**زر إدارة الموردين**:
- **Caption**: `إدارة الموردين`
- **الكود**:
```vba
Private Sub btnSuppliers_Click()
    DoCmd.OpenForm "نموذج_الموردين"
End Sub
```

**زر إدارة المواد الخام**:
- **Caption**: `إدارة المواد الخام`
- **الكود**:
```vba
Private Sub btnRawMaterials_Click()
    DoCmd.OpenForm "نموذج_المواد_الخام"
End Sub
```

**زر تقرير المخزون**:
- **Caption**: `تقرير المخزون`
- **الكود**:
```vba
Private Sub btnInventoryReport_Click()
    DoCmd.OpenReport "تقرير_المخزون", acViewPreview
End Sub
```

## 🎯 الخطوة 2: إنشاء التقارير العربية

### أ. إنشاء تقرير المخزون

#### 1. إنشاء الاستعلام أولاً:
1. **Create** > **Query Design**
2. **أغلق نافذة Show Table**
3. **انتقل إلى SQL View**
4. **انسخ والصق هذا الكود**:

```sql
SELECT 
    rm.MaterialName AS [اسم المادة],
    c.CategoryName AS [الفئة],
    u.UnitName AS [وحدة القياس],
    IIF(inv.AvailableQuantity IS NULL, 0, inv.AvailableQuantity) AS [الكمية المتاحة],
    IIF(inv.AverageCost IS NULL, 0, inv.AverageCost) AS [متوسط التكلفة],
    IIF(inv.TotalValue IS NULL, 0, inv.TotalValue) AS [إجمالي القيمة],
    rm.MinimumLevel AS [الحد الأدنى],
    IIF(IIF(inv.AvailableQuantity IS NULL, 0, inv.AvailableQuantity) <= rm.MinimumLevel, "تحت الحد الأدنى", "مناسب") AS [حالة المخزون]
FROM ((RawMaterials rm 
LEFT JOIN Categories c ON rm.CategoryID = c.CategoryID) 
LEFT JOIN Units u ON rm.UnitID = u.UnitID) 
LEFT JOIN RawMaterialsInventory inv ON rm.MaterialID = inv.MaterialID
ORDER BY c.CategoryName, rm.MaterialName
```

5. **احفظ الاستعلام باسم**: `استعلام_المخزون_العربي`

#### 2. إنشاء التقرير:
1. **Create** > **Report Wizard**
2. **اختر الاستعلام**: `استعلام_المخزون_العربي`
3. **اختر جميع الحقول**
4. **اختر Tabular Layout**
5. **احفظ باسم**: `تقرير_المخزون`

#### 3. تخصيص التقرير:
1. **انتقل إلى Design View**
2. **اضبط خصائص التقرير**:
   - **Caption**: `تقرير المخزون الشامل`
   - **Right To Left**: `Yes`
   - **Right To Left Layout**: `Yes`

3. **أضف عنوان في Report Header**:
   - **أضف Label**: `تقرير المخزون الشامل - مصنع المعمول والمخللات`
   - **Font Size**: `16`
   - **Font Bold**: `Yes`
   - **Text Align**: `Center`

4. **أضف تاريخ التقرير**:
   - **أضف Text Box**
   - **Control Source**: `=Now()`
   - **Format**: `dd/mm/yyyy`
   - **أضف Label**: `تاريخ التقرير:`

### ب. إنشاء تقرير الموردين

#### 1. إنشاء الاستعلام:
```sql
SELECT 
    s.SupplierName AS [اسم المورد],
    s.ContactPerson AS [جهة الاتصال],
    s.Phone AS [الهاتف],
    s.Email AS [البريد الإلكتروني],
    s.City AS [المدينة],
    s.CreditLimit AS [حد الائتمان],
    s.AccountBalance AS [رصيد الحساب],
    IIF(s.IsActive, "نشط", "غير نشط") AS [الحالة]
FROM Suppliers s
ORDER BY s.SupplierName
```

2. **احفظ باسم**: `استعلام_الموردين_العربي`
3. **أنشئ التقرير** باستخدام نفس الخطوات السابقة
4. **احفظ باسم**: `تقرير_الموردين`

## 🎯 الخطوة 3: كيفية الاستخدام

### أ. تشغيل النماذج:
1. **في Navigation Pane**، ابحث عن النماذج
2. **انقر مزدوجاً** على النموذج لفتحه
3. **استخدم الأزرار** للتنقل والحفظ

### ب. عرض التقارير:
1. **انقر مزدوجاً** على التقرير لعرضه
2. **استخدم Print Preview** للمعاينة
3. **اطبع** أو **صدّر** حسب الحاجة

### ج. إضافة بيانات جديدة:
1. **افتح النموذج المناسب**
2. **انقر زر "سجل جديد"**
3. **أدخل البيانات**
4. **انقر "حفظ"**

## 🔧 نصائح مهمة:

### للنماذج:
- **استخدم Tab Order** لترتيب التنقل بين الحقول
- **أضف Validation Rules** للتحقق من صحة البيانات
- **استخدم Combo Boxes** للقوائم المنسدلة

### للتقارير:
- **اختبر الطباعة** قبل الاستخدام النهائي
- **اضبط Page Setup** للحجم المناسب
- **استخدم Grouping** لتنظيم البيانات

### للأداء:
- **أغلق النماذج** غير المستخدمة
- **استخدم Filters** لتقليل البيانات المعروضة
- **احفظ النسخ الاحتياطية** بانتظام

---

## 🎊 الآن يمكنك البدء!

اتبع هذه الخطوات بالترتيب وستحصل على نماذج وتقارير عربية احترافية جاهزة للاستخدام!
