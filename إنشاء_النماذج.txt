كود VBA لإنشاء النماذج الأساسية في Microsoft Access
=======================================================

يجب نسخ هذا الكود في وحدة VBA جديدة في Access:

Sub CreateBasicForms()
    ' إنشاء نموذج الموردين
    CreateSuppliersForm
    
    ' إنشاء نموذج المواد الخام
    CreateRawMaterialsForm
    
    ' إنشاء نموذج فواتير الشراء
    CreatePurchaseInvoicesForm
    
    ' إنشاء نموذج المنتجات
    CreateProductsForm
    
    ' إنشاء نموذج أوامر الإنتاج
    CreateProductionOrdersForm
    
    ' إنشاء نموذج المخزون
    CreateInventoryForm
    
    MsgBox "تم إنشاء جميع النماذج بنجاح!", vbInformation
End Sub

Sub CreateSuppliersForm()
    Dim frm As Form
    Set frm = CreateForm()
    
    ' تعيين خصائص النموذج
    frm.Caption = "إدارة الموردين"
    frm.RecordSource = "Suppliers"
    frm.DefaultView = 0 ' Single Form
    frm.AllowAdditions = True
    frm.AllowDeletions = True
    frm.AllowEdits = True
    
    ' حفظ النموذج
    DoCmd.Save acForm, frm.Name
    DoCmd.Rename "نموذج_الموردين", acForm, frm.Name
End Sub

Sub CreateRawMaterialsForm()
    Dim frm As Form
    Set frm = CreateForm()
    
    ' تعيين خصائص النموذج
    frm.Caption = "إدارة المواد الخام"
    frm.RecordSource = "RawMaterials"
    frm.DefaultView = 0 ' Single Form
    frm.AllowAdditions = True
    frm.AllowDeletions = True
    frm.AllowEdits = True
    
    ' حفظ النموذج
    DoCmd.Save acForm, frm.Name
    DoCmd.Rename "نموذج_المواد_الخام", acForm, frm.Name
End Sub

Sub CreatePurchaseInvoicesForm()
    Dim frm As Form
    Set frm = CreateForm()
    
    ' تعيين خصائص النموذج
    frm.Caption = "فواتير الشراء"
    frm.RecordSource = "PurchaseInvoices"
    frm.DefaultView = 0 ' Single Form
    frm.AllowAdditions = True
    frm.AllowDeletions = True
    frm.AllowEdits = True
    
    ' حفظ النموذج
    DoCmd.Save acForm, frm.Name
    DoCmd.Rename "نموذج_فواتير_الشراء", acForm, frm.Name
End Sub

Sub CreateProductsForm()
    Dim frm As Form
    Set frm = CreateForm()
    
    ' تعيين خصائص النموذج
    frm.Caption = "إدارة المنتجات"
    frm.RecordSource = "Products"
    frm.DefaultView = 0 ' Single Form
    frm.AllowAdditions = True
    frm.AllowDeletions = True
    frm.AllowEdits = True
    
    ' حفظ النموذج
    DoCmd.Save acForm, frm.Name
    DoCmd.Rename "نموذج_المنتجات", acForm, frm.Name
End Sub

Sub CreateProductionOrdersForm()
    Dim frm As Form
    Set frm = CreateForm()
    
    ' تعيين خصائص النموذج
    frm.Caption = "أوامر الإنتاج"
    frm.RecordSource = "ProductionOrders"
    frm.DefaultView = 0 ' Single Form
    frm.AllowAdditions = True
    frm.AllowDeletions = True
    frm.AllowEdits = True
    
    ' حفظ النموذج
    DoCmd.Save acForm, frm.Name
    DoCmd.Rename "نموذج_أوامر_الإنتاج", acForm, frm.Name
End Sub

Sub CreateInventoryForm()
    Dim frm As Form
    Set frm = CreateForm()
    
    ' تعيين خصائص النموذج
    frm.Caption = "إدارة المخزون"
    frm.RecordSource = "RawMaterialsInventory"
    frm.DefaultView = 2 ' Datasheet
    frm.AllowAdditions = False
    frm.AllowDeletions = False
    frm.AllowEdits = True
    
    ' حفظ النموذج
    DoCmd.Save acForm, frm.Name
    DoCmd.Rename "نموذج_المخزون", acForm, frm.Name
End Sub

' إنشاء الاستعلامات الأساسية
Sub CreateBasicQueries()
    ' استعلام تقرير المخزون
    CreateInventoryReportQuery
    
    ' استعلام تكاليف الإنتاج
    CreateProductionCostQuery
    
    ' استعلام فواتير الشراء المفتوحة
    CreateOpenInvoicesQuery
    
    MsgBox "تم إنشاء جميع الاستعلامات بنجاح!", vbInformation
End Sub

Sub CreateInventoryReportQuery()
    Dim qdf As QueryDef
    Dim sql As String
    
    sql = "SELECT rm.MaterialName AS [اسم المادة], " & _
          "c.CategoryName AS [الفئة], " & _
          "u.UnitName AS [الوحدة], " & _
          "inv.AvailableQuantity AS [الكمية المتاحة], " & _
          "inv.AverageCost AS [متوسط التكلفة], " & _
          "inv.TotalValue AS [إجمالي القيمة], " & _
          "inv.MinimumLevel AS [الحد الأدنى] " & _
          "FROM ((RawMaterials rm " & _
          "LEFT JOIN Categories c ON rm.CategoryID = c.CategoryID) " & _
          "LEFT JOIN Units u ON rm.UnitID = u.UnitID) " & _
          "LEFT JOIN RawMaterialsInventory inv ON rm.MaterialID = inv.MaterialID " & _
          "WHERE rm.IsActive = True " & _
          "ORDER BY c.CategoryName, rm.MaterialName"
    
    Set qdf = CurrentDb.CreateQueryDef("تقرير_المخزون", sql)
End Sub

Sub CreateProductionCostQuery()
    Dim qdf As QueryDef
    Dim sql As String
    
    sql = "SELECT po.OrderID AS [رقم الأمر], " & _
          "p.ProductName AS [اسم المنتج], " & _
          "po.RequiredQuantity AS [الكمية المطلوبة], " & _
          "po.ProducedQuantity AS [الكمية المنتجة], " & _
          "po.EstimatedCost AS [التكلفة المقدرة], " & _
          "po.ActualCost AS [التكلفة الفعلية], " & _
          "po.OrderStatus AS [حالة الأمر], " & _
          "po.OrderDate AS [تاريخ الأمر] " & _
          "FROM ProductionOrders po " & _
          "LEFT JOIN Products p ON po.ProductID = p.ProductID " & _
          "ORDER BY po.OrderDate DESC"
    
    Set qdf = CurrentDb.CreateQueryDef("تقرير_تكاليف_الإنتاج", sql)
End Sub

Sub CreateOpenInvoicesQuery()
    Dim qdf As QueryDef
    Dim sql As String
    
    sql = "SELECT pi.InvoiceID AS [رقم الفاتورة], " & _
          "s.SupplierName AS [اسم المورد], " & _
          "pi.InvoiceDate AS [تاريخ الفاتورة], " & _
          "pi.DueDate AS [تاريخ الاستحقاق], " & _
          "pi.TotalAmount AS [إجمالي الفاتورة], " & _
          "pi.PaidAmount AS [المبلغ المدفوع], " & _
          "pi.RemainingAmount AS [المبلغ المتبقي], " & _
          "pi.InvoiceStatus AS [حالة الفاتورة] " & _
          "FROM PurchaseInvoices pi " & _
          "LEFT JOIN Suppliers s ON pi.SupplierID = s.SupplierID " & _
          "WHERE pi.InvoiceStatus = 'Open' " & _
          "ORDER BY pi.DueDate"
    
    Set qdf = CurrentDb.CreateQueryDef("الفواتير_المفتوحة", sql)
End Sub

' إنشاء التقارير الأساسية
Sub CreateBasicReports()
    ' تقرير المخزون
    CreateInventoryReport
    
    ' تقرير تكاليف الإنتاج
    CreateProductionCostReport
    
    ' تقرير الفواتير المفتوحة
    CreateOpenInvoicesReport
    
    MsgBox "تم إنشاء جميع التقارير بنجاح!", vbInformation
End Sub

Sub CreateInventoryReport()
    Dim rpt As Report
    Set rpt = CreateReport()
    
    ' تعيين خصائص التقرير
    rpt.Caption = "تقرير المخزون"
    rpt.RecordSource = "تقرير_المخزون"
    
    ' حفظ التقرير
    DoCmd.Save acReport, rpt.Name
    DoCmd.Rename "تقرير_المخزون", acReport, rpt.Name
End Sub

Sub CreateProductionCostReport()
    Dim rpt As Report
    Set rpt = CreateReport()
    
    ' تعيين خصائص التقرير
    rpt.Caption = "تقرير تكاليف الإنتاج"
    rpt.RecordSource = "تقرير_تكاليف_الإنتاج"
    
    ' حفظ التقرير
    DoCmd.Save acReport, rpt.Name
    DoCmd.Rename "تقرير_تكاليف_الإنتاج", acReport, rpt.Name
End Sub

Sub CreateOpenInvoicesReport()
    Dim rpt As Report
    Set rpt = CreateReport()
    
    ' تعيين خصائص التقرير
    rpt.Caption = "تقرير الفواتير المفتوحة"
    rpt.RecordSource = "الفواتير_المفتوحة"
    
    ' حفظ التقرير
    DoCmd.Save acReport, rpt.Name
    DoCmd.Rename "تقرير_الفواتير_المفتوحة", acReport, rpt.Name
End Sub
