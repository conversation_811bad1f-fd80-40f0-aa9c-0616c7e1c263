# سكريبت PowerShell لإنشاء نظام محاسبة المصنع
try {
    # إنشاء كائن Access
    $accessApp = New-Object -ComObject Access.Application
    $accessApp.Visible = $true
    
    # إنشاء قاعدة بيانات جديدة
    $dbPath = "D:\QQ@WORK1\الاجازات\23\Factory_Accounting_System.accdb"
    $accessApp.NewCurrentDatabase($dbPath)
    $db = $accessApp.CurrentDb
    
    Write-Host "تم إنشاء قاعدة البيانات بنجاح"
    
    # إنشاء جدول وحدات القياس
    $sql = "CREATE TABLE Units (UnitID AUTOINCREMENT PRIMARY KEY, UnitName TEXT(50) NOT NULL, Symbol TEXT(10), Notes MEMO)"
    $db.Execute($sql)
    Write-Host "تم إنشاء جدول وحدات القياس"
    
    # إنشاء جدول العملات
    $sql = "CREATE TABLE Currencies (CurrencyID AUTOINCREMENT PRIMARY KEY, CurrencyName TEXT(50) NOT NULL, Symbol TEXT(10), ExchangeRate CURRENCY DEFAULT 1, UpdateDate DATETIME DEFAULT Now())"
    $db.Execute($sql)
    Write-Host "تم إنشاء جدول العملات"
    
    # إنشاء جدول فئات المواد
    $sql = "CREATE TABLE Categories (CategoryID AUTOINCREMENT PRIMARY KEY, CategoryName TEXT(100) NOT NULL, Description MEMO, CategoryType TEXT(20))"
    $db.Execute($sql)
    Write-Host "تم إنشاء جدول فئات المواد"
    
    # إنشاء جدول المواد الخام
    $sql = "CREATE TABLE RawMaterials (MaterialID AUTOINCREMENT PRIMARY KEY, MaterialName TEXT(100) NOT NULL, CategoryID LONG, UnitID LONG, MinimumLevel DOUBLE DEFAULT 0, AveragePrice CURRENCY DEFAULT 0, Notes MEMO, CreatedDate DATETIME DEFAULT Now(), IsActive YESNO DEFAULT True)"
    $db.Execute($sql)
    Write-Host "تم إنشاء جدول المواد الخام"
    
    # إنشاء جدول الموردين
    $sql = "CREATE TABLE Suppliers (SupplierID AUTOINCREMENT PRIMARY KEY, SupplierName TEXT(100) NOT NULL, ContactPerson TEXT(100), Phone TEXT(20), Mobile TEXT(20), Email TEXT(100), Address MEMO, City TEXT(50), Country TEXT(50), TaxNumber TEXT(50), PaymentTerms TEXT(100), CreditDays INTEGER DEFAULT 0, CreditLimit CURRENCY DEFAULT 0, AccountBalance CURRENCY DEFAULT 0, RegistrationDate DATETIME DEFAULT Now(), IsActive YESNO DEFAULT True, Notes MEMO)"
    $db.Execute($sql)
    Write-Host "تم إنشاء جدول الموردين"
    
    # إنشاء جدول فواتير الشراء
    $sql = "CREATE TABLE PurchaseInvoices (InvoiceID AUTOINCREMENT PRIMARY KEY, SupplierInvoiceNumber TEXT(50), SupplierID LONG NOT NULL, InvoiceDate DATETIME DEFAULT Now(), DueDate DATETIME, CurrencyID LONG DEFAULT 1, ExchangeRate CURRENCY DEFAULT 1, SubTotal CURRENCY DEFAULT 0, TaxAmount CURRENCY DEFAULT 0, TaxRate DOUBLE DEFAULT 0, TotalAmount CURRENCY DEFAULT 0, PaidAmount CURRENCY DEFAULT 0, RemainingAmount CURRENCY DEFAULT 0, InvoiceStatus TEXT(20) DEFAULT 'Open', Notes MEMO, EntryDate DATETIME DEFAULT Now(), EntryUser TEXT(50))"
    $db.Execute($sql)
    Write-Host "تم إنشاء جدول فواتير الشراء"
    
    # إنشاء جدول تفاصيل فواتير الشراء
    $sql = "CREATE TABLE PurchaseInvoiceDetails (DetailID AUTOINCREMENT PRIMARY KEY, InvoiceID LONG NOT NULL, MaterialID LONG NOT NULL, Quantity DOUBLE NOT NULL, UnitPrice CURRENCY NOT NULL, LineTotal CURRENCY, DiscountPercent DOUBLE DEFAULT 0, DiscountAmount CURRENCY DEFAULT 0, NetAmount CURRENCY, ExpiryDate DATETIME, LotNumber TEXT(50), Notes MEMO)"
    $db.Execute($sql)
    Write-Host "تم إنشاء جدول تفاصيل فواتير الشراء"
    
    # إنشاء جدول المنتجات
    $sql = "CREATE TABLE Products (ProductID AUTOINCREMENT PRIMARY KEY, ProductName TEXT(100) NOT NULL, CategoryID LONG, UnitID LONG, SalePrice CURRENCY DEFAULT 0, ProductionCost CURRENCY DEFAULT 0, ProductionTime INTEGER DEFAULT 0, MinimumLevel DOUBLE DEFAULT 0, Description MEMO, CreatedDate DATETIME DEFAULT Now(), IsActive YESNO DEFAULT True)"
    $db.Execute($sql)
    Write-Host "تم إنشاء جدول المنتجات"
    
    # إنشاء جدول وصفات الإنتاج
    $sql = "CREATE TABLE ProductRecipes (RecipeID AUTOINCREMENT PRIMARY KEY, ProductID LONG NOT NULL, MaterialID LONG NOT NULL, RequiredQuantity DOUBLE NOT NULL, MaterialCost CURRENCY DEFAULT 0, Notes MEMO)"
    $db.Execute($sql)
    Write-Host "تم إنشاء جدول وصفات الإنتاج"
    
    # إنشاء جدول مراحل التصنيع
    $sql = "CREATE TABLE ProductionStages (StageID AUTOINCREMENT PRIMARY KEY, StageName TEXT(100) NOT NULL, Description MEMO, StageOrder INTEGER, StageCost CURRENCY DEFAULT 0, StageTime INTEGER DEFAULT 0)"
    $db.Execute($sql)
    Write-Host "تم إنشاء جدول مراحل التصنيع"
    
    # إنشاء جدول أوامر الإنتاج
    $sql = "CREATE TABLE ProductionOrders (OrderID AUTOINCREMENT PRIMARY KEY, ProductID LONG NOT NULL, RequiredQuantity DOUBLE NOT NULL, ProducedQuantity DOUBLE DEFAULT 0, OrderDate DATETIME DEFAULT Now(), StartDate DATETIME, EndDate DATETIME, OrderStatus TEXT(20) DEFAULT 'New', EstimatedCost CURRENCY DEFAULT 0, ActualCost CURRENCY DEFAULT 0, Notes MEMO, EntryUser TEXT(50))"
    $db.Execute($sql)
    Write-Host "تم إنشاء جدول أوامر الإنتاج"
    
    # إنشاء جدول مخزون المواد الخام
    $sql = "CREATE TABLE RawMaterialsInventory (InventoryID AUTOINCREMENT PRIMARY KEY, MaterialID LONG NOT NULL, AvailableQuantity DOUBLE DEFAULT 0, AverageCost CURRENCY DEFAULT 0, TotalValue CURRENCY DEFAULT 0, LastMovementDate DATETIME, MinimumLevel DOUBLE DEFAULT 0, MaximumLevel DOUBLE DEFAULT 0, ReorderPoint DOUBLE DEFAULT 0, Notes MEMO)"
    $db.Execute($sql)
    Write-Host "تم إنشاء جدول مخزون المواد الخام"
    
    # إنشاء جدول مخزون المنتجات النهائية
    $sql = "CREATE TABLE FinishedProductsInventory (InventoryID AUTOINCREMENT PRIMARY KEY, ProductID LONG NOT NULL, AvailableQuantity DOUBLE DEFAULT 0, AverageCost CURRENCY DEFAULT 0, TotalValue CURRENCY DEFAULT 0, LastMovementDate DATETIME, MinimumLevel DOUBLE DEFAULT 0, MaximumLevel DOUBLE DEFAULT 0, ExpiryDate DATETIME, LotNumber TEXT(50), Notes MEMO)"
    $db.Execute($sql)
    Write-Host "تم إنشاء جدول مخزون المنتجات النهائية"
    
    # إنشاء جدول حركات المخزون
    $sql = "CREATE TABLE InventoryMovements (MovementID AUTOINCREMENT PRIMARY KEY, MovementType TEXT(20) NOT NULL, ItemType TEXT(20) NOT NULL, ItemID LONG NOT NULL, Quantity DOUBLE NOT NULL, UnitPrice CURRENCY DEFAULT 0, TotalValue CURRENCY DEFAULT 0, MovementDate DATETIME DEFAULT Now(), ReferenceNumber LONG, ReferenceType TEXT(20), BalanceBefore DOUBLE DEFAULT 0, BalanceAfter DOUBLE DEFAULT 0, Notes MEMO, EntryUser TEXT(50))"
    $db.Execute($sql)
    Write-Host "تم إنشاء جدول حركات المخزون"
    
    Write-Host "تم إنشاء جميع الجداول الأساسية بنجاح!"
    
    # إبقاء Access مفتوحاً للمستخدم
    Write-Host "تم فتح Microsoft Access. يمكنك الآن رؤية قاعدة البيانات."
}
catch {
    Write-Host "خطأ في إنشاء الجداول: $($_.Exception.Message)"
}
