# 🚀 دليل التشغيل السريع لنظام محاسبة مصنع المعمول والمخللات

## المتطلبات الأساسية
- Microsoft Access 2016 أو أحدث
- Windows 10 أو أحدث
- 4 جيجابايت RAM كحد أدنى

## 📁 الملفات الموجودة
- **Factory_Accounting_System.accdb** - ملف قاعدة البيانات الرئيسي
- **دليل_الاستخدام.md** - دليل الاستخدام التفصيلي
- **تقرير_الاختبار_الشامل.md** - تقرير الاختبار الشامل

## 🎯 خطوات التشغيل السريع

### الخطوة 1: فتح قاعدة البيانات
1. انقر نقراً مزدوجاً على ملف `Factory_Accounting_System.accdb`
2. إذا ظهرت رسالة أمان، اختر "Enable Content" أو "تمكين المحتوى"
3. ستفتح قاعدة البيانات وتظهر لك جميع الجداول

### الخطوة 2: استكشاف الجداول الموجودة
النظام يحتوي على الجداول التالية:

#### جداول البيانات الأساسية:
- **Units** - وحدات القياس (كجم، لتر، قطعة، إلخ)
- **Currencies** - العملات (ريال، دولار، يورو)
- **Categories** - فئات المواد والمنتجات

#### جداول إدارة المشتريات:
- **Suppliers** - بيانات الموردين
- **RawMaterials** - المواد الخام
- **PurchaseInvoices** - فواتير الشراء
- **PurchaseInvoiceDetails** - تفاصيل فواتير الشراء

#### جداول إدارة الإنتاج:
- **Products** - المنتجات النهائية
- **ProductRecipes** - وصفات الإنتاج
- **ProductionStages** - مراحل التصنيع
- **ProductionOrders** - أوامر الإنتاج
- **ProductionCosts** - تكاليف الإنتاج

#### جداول إدارة المخزون:
- **RawMaterialsInventory** - مخزون المواد الخام
- **FinishedProductsInventory** - مخزون المنتجات النهائية
- **InventoryMovements** - حركات المخزون

### الخطوة 3: البدء بإدخال البيانات

#### أ. إدخال البيانات الأساسية (إذا لم تكن موجودة):
1. افتح جدول **Units** وأدخل وحدات القياس:
   - كيلوجرام (كجم)
   - جرام (جم)
   - لتر (لتر)
   - قطعة (قطعة)

2. افتح جدول **Currencies** وأدخل العملات:
   - الريال السعودي (ريال) - سعر الصرف: 1.00
   - الدولار الأمريكي ($) - سعر الصرف: 3.75

3. افتح جدول **Categories** وأدخل الفئات:
   - دقيق ونشويات (مواد خام)
   - سكر ومحليات (مواد خام)
   - زيوت ودهون (مواد خام)
   - معمول (منتجات نهائية)
   - مخللات (منتجات نهائية)

#### ب. إدخال بيانات الموردين:
1. افتح جدول **Suppliers**
2. أدخل بيانات الموردين:
   - اسم المورد
   - جهة الاتصال
   - الهاتف والجوال
   - البريد الإلكتروني
   - العنوان والمدينة
   - شروط الدفع
   - حد الائتمان

#### ج. إدخال المواد الخام:
1. افتح جدول **RawMaterials**
2. أدخل المواد الخام مثل:
   - دقيق أبيض فاخر
   - سكر أبيض
   - زيت زيتون
   - تمر مجفف
   - جوز مقشر

#### د. إدخال المنتجات النهائية:
1. افتح جدول **Products**
2. أدخل المنتجات مثل:
   - معمول بالتمر - حجم صغير
   - معمول بالجوز
   - مخلل خيار
   - مخلل جزر

### الخطوة 4: العمليات اليومية

#### إنشاء فاتورة شراء:
1. افتح جدول **PurchaseInvoices**
2. أدخل بيانات الفاتورة:
   - رقم فاتورة المورد
   - المورد
   - تاريخ الفاتورة
   - إجمالي الفاتورة

3. افتح جدول **PurchaseInvoiceDetails**
4. أدخل تفاصيل الفاتورة:
   - المادة الخام
   - الكمية
   - سعر الوحدة

#### إنشاء أمر إنتاج:
1. افتح جدول **ProductionOrders**
2. أدخل بيانات الأمر:
   - المنتج
   - الكمية المطلوبة
   - تاريخ الأمر
   - التكلفة المقدرة

### الخطوة 5: عرض التقارير

#### تقرير المخزون:
```sql
SELECT 
    rm.MaterialName AS [اسم المادة],
    inv.AvailableQuantity AS [الكمية المتاحة],
    inv.AverageCost AS [متوسط التكلفة],
    inv.TotalValue AS [إجمالي القيمة]
FROM RawMaterials rm 
LEFT JOIN RawMaterialsInventory inv ON rm.MaterialID = inv.MaterialID
WHERE rm.IsActive = True
```

#### تقرير الموردين:
```sql
SELECT 
    SupplierName AS [اسم المورد],
    ContactPerson AS [جهة الاتصال],
    Phone AS [الهاتف],
    CreditLimit AS [حد الائتمان],
    AccountBalance AS [رصيد الحساب]
FROM Suppliers 
WHERE IsActive = True
```

## 🔧 نصائح مهمة

### 1. النسخ الاحتياطي:
- اعمل نسخة احتياطية من ملف `.accdb` يومياً
- احفظ النسخ في مكان آمن

### 2. إدخال البيانات:
- تأكد من إدخال البيانات الأساسية أولاً
- استخدم أرقام مرجعية واضحة
- تحقق من صحة البيانات قبل الحفظ

### 3. الأمان:
- لا تشارك ملف قاعدة البيانات عبر الشبكة
- استخدم كلمات مرور قوية إذا لزم الأمر

### 4. الأداء:
- أغلق الجداول غير المستخدمة
- اضغط قاعدة البيانات دورياً (Compact & Repair)

## 🆘 حل المشاكل الشائعة

### مشكلة: لا يمكن فتح قاعدة البيانات
**الحل**: تأكد من وجود Microsoft Access وأن الملف غير تالف

### مشكلة: رسالة أمان عند الفتح
**الحل**: اختر "Enable Content" أو "تمكين المحتوى"

### مشكلة: بطء في الأداء
**الحل**: 
- أغلق البرامج غير الضرورية
- اضغط قاعدة البيانات (Tools > Database Tools > Compact & Repair)

### مشكلة: خطأ في إدخال البيانات
**الحل**: تحقق من:
- صحة نوع البيانات
- وجود البيانات المرجعية (مثل المورد قبل إدخال الفاتورة)
- عدم تكرار المفاتيح الأساسية

## 📞 الدعم الفني

للحصول على المساعدة:
1. راجع **دليل_الاستخدام.md** للتفاصيل الكاملة
2. راجع **تقرير_الاختبار_الشامل.md** للمشاكل المعروفة
3. تواصل مع فريق التطوير للدعم المتقدم

---

**مبروك! نظامك جاهز للاستخدام** 🎉
