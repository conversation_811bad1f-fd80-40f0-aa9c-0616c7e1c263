-- الاستعلامات العربية لنظام محاسبة مصنع المعمول والمخللات
-- ================================================================

-- 1. تقرير المخزون الشامل
-- ========================
CREATE VIEW تقرير_المخزون_الشامل AS
SELECT 
    rm.MaterialName AS [اسم المادة],
    c.CategoryName AS [الفئة],
    u.UnitName AS [وحدة القياس],
    ISNULL(inv.AvailableQuantity, 0) AS [الكمية المتاحة],
    ISNULL(inv.AverageCost, 0) AS [متوسط التكلفة],
    ISNULL(inv.TotalValue, 0) AS [إجمالي القيمة],
    rm.MinimumLevel AS [الحد الأدنى],
    IIF(ISNULL(inv.AvailableQuantity, 0) <= rm.MinimumLevel, "تحت الحد الأدنى", "مناسب") AS [حالة المخزون],
    inv.LastMovementDate AS [تاريخ آخر حركة],
    rm.AveragePrice AS [متوسط سعر الشراء],
    IIF(rm.IsActive, "نشط", "غير نشط") AS [الحالة]
FROM ((RawMaterials rm 
LEFT JOIN Categories c ON rm.CategoryID = c.CategoryID) 
LEFT JOIN Units u ON rm.UnitID = u.UnitID) 
LEFT JOIN RawMaterialsInventory inv ON rm.MaterialID = inv.MaterialID
ORDER BY c.CategoryName, rm.MaterialName;

-- 2. تقرير تكاليف الإنتاج التفصيلي
-- ==================================
CREATE VIEW تقرير_تكاليف_الإنتاج_التفصيلي AS
SELECT 
    po.OrderID AS [رقم أمر الإنتاج],
    p.ProductName AS [اسم المنتج],
    c.CategoryName AS [فئة المنتج],
    po.RequiredQuantity AS [الكمية المطلوبة],
    po.ProducedQuantity AS [الكمية المنتجة],
    ROUND((po.ProducedQuantity / po.RequiredQuantity) * 100, 2) AS [نسبة الإنجاز %],
    po.EstimatedCost AS [التكلفة المقدرة],
    ISNULL(po.ActualCost, 0) AS [التكلفة الفعلية],
    (ISNULL(po.ActualCost, 0) - po.EstimatedCost) AS [انحراف التكلفة],
    IIF(ISNULL(po.ActualCost, 0) > po.EstimatedCost, "تجاوز الميزانية", "ضمن الميزانية") AS [حالة التكلفة],
    po.OrderDate AS [تاريخ الأمر],
    po.StartDate AS [تاريخ البدء],
    po.EndDate AS [تاريخ الانتهاء],
    IIF(po.EndDate IS NULL, DATEDIFF("d", po.StartDate, Date()), DATEDIFF("d", po.StartDate, po.EndDate)) AS [مدة الإنتاج بالأيام],
    po.OrderStatus AS [حالة الأمر],
    po.EntryUser AS [المستخدم المسؤول]
FROM (ProductionOrders po 
LEFT JOIN Products p ON po.ProductID = p.ProductID)
LEFT JOIN Categories c ON p.CategoryID = c.CategoryID
ORDER BY po.OrderDate DESC;

-- 3. تقرير الفواتير المستحقة والمتأخرة
-- ====================================
CREATE VIEW تقرير_الفواتير_المستحقة_والمتأخرة AS
SELECT 
    pi.InvoiceID AS [رقم الفاتورة],
    pi.SupplierInvoiceNumber AS [رقم فاتورة المورد],
    s.SupplierName AS [اسم المورد],
    s.ContactPerson AS [جهة الاتصال],
    s.Phone AS [رقم الهاتف],
    pi.InvoiceDate AS [تاريخ الفاتورة],
    pi.DueDate AS [تاريخ الاستحقاق],
    pi.TotalAmount AS [إجمالي الفاتورة],
    pi.PaidAmount AS [المبلغ المدفوع],
    pi.RemainingAmount AS [المبلغ المتبقي],
    DATEDIFF("d", pi.DueDate, Date()) AS [أيام التأخير],
    IIF(pi.DueDate < Date(), "متأخرة", 
        IIF(pi.DueDate = Date(), "مستحقة اليوم", "غير مستحقة")) AS [حالة الاستحقاق],
    IIF(DATEDIFF("d", pi.DueDate, Date()) > 30, "خطر عالي",
        IIF(DATEDIFF("d", pi.DueDate, Date()) > 7, "خطر متوسط", "عادي")) AS [مستوى الخطر],
    pi.InvoiceStatus AS [حالة الفاتورة],
    cur.CurrencyName AS [العملة],
    pi.ExchangeRate AS [سعر الصرف]
FROM (PurchaseInvoices pi 
LEFT JOIN Suppliers s ON pi.SupplierID = s.SupplierID)
LEFT JOIN Currencies cur ON pi.CurrencyID = cur.CurrencyID
WHERE pi.InvoiceStatus = 'Open' AND pi.RemainingAmount > 0
ORDER BY [أيام التأخير] DESC, pi.RemainingAmount DESC;

-- 4. تقرير تحليل الأرباح والمبيعات
-- ================================
CREATE VIEW تقرير_تحليل_الأرباح_والمبيعات AS
SELECT 
    p.ProductName AS [اسم المنتج],
    c.CategoryName AS [فئة المنتج],
    u.UnitName AS [وحدة القياس],
    p.SalePrice AS [سعر البيع],
    p.ProductionCost AS [تكلفة الإنتاج],
    (p.SalePrice - p.ProductionCost) AS [هامش الربح],
    IIF(p.ProductionCost > 0, 
        ROUND(((p.SalePrice - p.ProductionCost) / p.ProductionCost) * 100, 2), 0) AS [نسبة الربح %],
    ISNULL(inv.AvailableQuantity, 0) AS [الكمية المتاحة],
    (ISNULL(inv.AvailableQuantity, 0) * p.SalePrice) AS [قيمة المخزون بسعر البيع],
    (ISNULL(inv.AvailableQuantity, 0) * p.ProductionCost) AS [قيمة المخزون بالتكلفة],
    ((ISNULL(inv.AvailableQuantity, 0) * p.SalePrice) - 
     (ISNULL(inv.AvailableQuantity, 0) * p.ProductionCost)) AS [الربح المتوقع من المخزون],
    p.MinimumLevel AS [الحد الأدنى],
    IIF(ISNULL(inv.AvailableQuantity, 0) <= p.MinimumLevel, "تحت الحد الأدنى", "مناسب") AS [حالة المخزون],
    p.ProductionTime AS [وقت الإنتاج بالدقائق],
    IIF(p.IsActive, "نشط", "غير نشط") AS [حالة المنتج]
FROM ((Products p 
LEFT JOIN Categories c ON p.CategoryID = c.CategoryID)
LEFT JOIN Units u ON p.UnitID = u.UnitID)
LEFT JOIN FinishedProductsInventory inv ON p.ProductID = inv.ProductID
WHERE p.IsActive = True
ORDER BY [نسبة الربح %] DESC;

-- 5. تقرير حركات المخزون التفصيلية
-- =================================
CREATE VIEW تقرير_حركات_المخزون_التفصيلية AS
SELECT 
    im.MovementDate AS [تاريخ الحركة],
    IIF(im.MovementType = 'Purchase', 'شراء',
        IIF(im.MovementType = 'Production', 'إنتاج',
            IIF(im.MovementType = 'Sale', 'بيع',
                IIF(im.MovementType = 'Adjustment', 'تسوية', im.MovementType)))) AS [نوع الحركة],
    IIF(im.ItemType = 'Raw Material', 'مادة خام', 'منتج نهائي') AS [نوع المادة],
    IIF(im.ItemType = 'Raw Material', rm.MaterialName, p.ProductName) AS [اسم المادة/المنتج],
    IIF(im.ItemType = 'Raw Material', c1.CategoryName, c2.CategoryName) AS [الفئة],
    IIF(im.ItemType = 'Raw Material', u1.UnitName, u2.UnitName) AS [وحدة القياس],
    im.Quantity AS [الكمية],
    im.UnitPrice AS [سعر الوحدة],
    im.TotalValue AS [إجمالي القيمة],
    im.BalanceBefore AS [الرصيد قبل الحركة],
    im.BalanceAfter AS [الرصيد بعد الحركة],
    (im.BalanceAfter - im.BalanceBefore) AS [صافي التغيير],
    IIF(im.ReferenceType = 'Purchase Invoice', 'فاتورة شراء',
        IIF(im.ReferenceType = 'Production Order', 'أمر إنتاج',
            IIF(im.ReferenceType = 'Sale Invoice', 'فاتورة بيع', im.ReferenceType))) AS [نوع المرجع],
    im.ReferenceNumber AS [رقم المرجع],
    im.Notes AS [ملاحظات],
    im.EntryUser AS [المستخدم]
FROM ((((InventoryMovements im 
LEFT JOIN RawMaterials rm ON im.ItemID = rm.MaterialID AND im.ItemType = 'Raw Material')
LEFT JOIN Products p ON im.ItemID = p.ProductID AND im.ItemType = 'Finished Product')
LEFT JOIN Categories c1 ON rm.CategoryID = c1.CategoryID)
LEFT JOIN Categories c2 ON p.CategoryID = c2.CategoryID)
LEFT JOIN Units u1 ON rm.UnitID = u1.UnitID
LEFT JOIN Units u2 ON p.UnitID = u2.UnitID
ORDER BY im.MovementDate DESC, im.MovementID DESC;

-- 6. تقرير الموردين والأرصدة
-- ===========================
CREATE VIEW تقرير_الموردين_والأرصدة AS
SELECT
    s.SupplierName AS [اسم المورد],
    s.ContactPerson AS [جهة الاتصال],
    s.Phone AS [الهاتف],
    s.Mobile AS [الجوال],
    s.Email AS [البريد الإلكتروني],
    s.City AS [المدينة],
    s.Country AS [الدولة],
    s.PaymentTerms AS [شروط الدفع],
    s.CreditDays AS [مدة الائتمان بالأيام],
    s.CreditLimit AS [حد الائتمان],
    s.AccountBalance AS [رصيد الحساب],
    (s.CreditLimit - s.AccountBalance) AS [الائتمان المتاح],
    IIF(s.AccountBalance > s.CreditLimit, "تجاوز الحد", "ضمن الحد") AS [حالة الائتمان],
    COUNT(pi.InvoiceID) AS [عدد الفواتير],
    ISNULL(SUM(pi.TotalAmount), 0) AS [إجمالي المشتريات],
    ISNULL(SUM(pi.PaidAmount), 0) AS [إجمالي المدفوع],
    ISNULL(SUM(pi.RemainingAmount), 0) AS [إجمالي المستحق],
    IIF(COUNT(pi.InvoiceID) > 0,
        ROUND(ISNULL(SUM(pi.TotalAmount), 0) / COUNT(pi.InvoiceID), 2), 0) AS [متوسط قيمة الفاتورة],
    MAX(pi.InvoiceDate) AS [تاريخ آخر فاتورة],
    DATEDIFF("d", MAX(pi.InvoiceDate), Date()) AS [أيام منذ آخر فاتورة],
    s.RegistrationDate AS [تاريخ التسجيل],
    IIF(s.IsActive, "نشط", "غير نشط") AS [الحالة],
    s.TaxNumber AS [الرقم الضريبي]
FROM Suppliers s
LEFT JOIN PurchaseInvoices pi ON s.SupplierID = pi.SupplierID
GROUP BY s.SupplierID, s.SupplierName, s.ContactPerson, s.Phone, s.Mobile, s.Email,
         s.City, s.Country, s.PaymentTerms, s.CreditDays, s.CreditLimit, s.AccountBalance,
         s.RegistrationDate, s.IsActive, s.TaxNumber
ORDER BY [إجمالي المشتريات] DESC;

-- 7. تقرير استهلاك المواد الخام
-- =============================
CREATE VIEW تقرير_استهلاك_المواد_الخام AS
SELECT
    rm.MaterialName AS [اسم المادة],
    c.CategoryName AS [الفئة],
    u.UnitName AS [وحدة القياس],
    COUNT(DISTINCT po.OrderID) AS [عدد أوامر الإنتاج],
    ISNULL(SUM(pr.RequiredQuantity * po.RequiredQuantity), 0) AS [إجمالي الكمية المطلوبة],
    ISNULL(SUM(pr.RequiredQuantity * po.ProducedQuantity), 0) AS [إجمالي الكمية المستهلكة],
    ISNULL(AVG(rm.AveragePrice), 0) AS [متوسط سعر الشراء],
    ISNULL(SUM(pr.RequiredQuantity * po.ProducedQuantity * rm.AveragePrice), 0) AS [إجمالي تكلفة الاستهلاك],
    IIF(COUNT(DISTINCT po.OrderID) > 0,
        ROUND(ISNULL(SUM(pr.RequiredQuantity * po.ProducedQuantity), 0) / COUNT(DISTINCT po.OrderID), 2), 0) AS [متوسط الاستهلاك لكل أمر],
    MIN(po.OrderDate) AS [أول استخدام],
    MAX(po.OrderDate) AS [آخر استخدام],
    DATEDIFF("d", MIN(po.OrderDate), MAX(po.OrderDate)) AS [فترة الاستخدام بالأيام],
    ISNULL(inv.AvailableQuantity, 0) AS [الكمية المتاحة حالياً],
    rm.MinimumLevel AS [الحد الأدنى],
    IIF(ISNULL(inv.AvailableQuantity, 0) <= rm.MinimumLevel, "تحت الحد الأدنى", "مناسب") AS [حالة المخزون],
    IIF(rm.IsActive, "نشط", "غير نشط") AS [حالة المادة]
FROM (((RawMaterials rm
LEFT JOIN Categories c ON rm.CategoryID = c.CategoryID)
LEFT JOIN Units u ON rm.UnitID = u.UnitID)
LEFT JOIN ProductRecipes pr ON rm.MaterialID = pr.MaterialID)
LEFT JOIN ProductionOrders po ON pr.ProductID = po.ProductID
LEFT JOIN RawMaterialsInventory inv ON rm.MaterialID = inv.MaterialID
WHERE po.OrderStatus IN ('Complete', 'مكتمل')
GROUP BY rm.MaterialID, rm.MaterialName, c.CategoryName, u.UnitName, rm.AveragePrice,
         inv.AvailableQuantity, rm.MinimumLevel, rm.IsActive
HAVING ISNULL(SUM(pr.RequiredQuantity * po.ProducedQuantity), 0) > 0
ORDER BY [إجمالي تكلفة الاستهلاك] DESC;

-- 8. تقرير كفاءة الإنتاج
-- ======================
CREATE VIEW تقرير_كفاءة_الإنتاج AS
SELECT
    p.ProductName AS [اسم المنتج],
    c.CategoryName AS [فئة المنتج],
    COUNT(po.OrderID) AS [عدد أوامر الإنتاج],
    ISNULL(SUM(po.RequiredQuantity), 0) AS [إجمالي الكمية المطلوبة],
    ISNULL(SUM(po.ProducedQuantity), 0) AS [إجمالي الكمية المنتجة],
    IIF(ISNULL(SUM(po.RequiredQuantity), 0) > 0,
        ROUND((ISNULL(SUM(po.ProducedQuantity), 0) / ISNULL(SUM(po.RequiredQuantity), 0)) * 100, 2), 0) AS [نسبة الإنجاز %],
    ISNULL(AVG(po.EstimatedCost), 0) AS [متوسط التكلفة المقدرة],
    ISNULL(AVG(po.ActualCost), 0) AS [متوسط التكلفة الفعلية],
    IIF(ISNULL(AVG(po.EstimatedCost), 0) > 0,
        ROUND(((ISNULL(AVG(po.ActualCost), 0) - ISNULL(AVG(po.EstimatedCost), 0)) / ISNULL(AVG(po.EstimatedCost), 0)) * 100, 2), 0) AS [انحراف التكلفة %],
    ISNULL(AVG(DATEDIFF("d", po.StartDate, po.EndDate)), 0) AS [متوسط مدة الإنتاج بالأيام],
    COUNT(IIF(po.OrderStatus IN ('Complete', 'مكتمل'), 1, NULL)) AS [الأوامر المكتملة],
    COUNT(IIF(po.OrderStatus IN ('In Progress', 'قيد التنفيذ'), 1, NULL)) AS [الأوامر قيد التنفيذ],
    COUNT(IIF(po.OrderStatus IN ('Delayed', 'متأخر'), 1, NULL)) AS [الأوامر المتأخرة],
    COUNT(IIF(po.OrderStatus IN ('New', 'جديد'), 1, NULL)) AS [الأوامر الجديدة],
    IIF(COUNT(po.OrderID) > 0,
        ROUND(COUNT(IIF(po.OrderStatus IN ('Complete', 'مكتمل'), 1, NULL)) / COUNT(po.OrderID) * 100, 2), 0) AS [نسبة الإنجاز الكلية %],
    p.ProductionTime AS [وقت الإنتاج المعياري بالدقائق],
    IIF(p.IsActive, "نشط", "غير نشط") AS [حالة المنتج]
FROM (Products p
LEFT JOIN Categories c ON p.CategoryID = c.CategoryID)
LEFT JOIN ProductionOrders po ON p.ProductID = po.ProductID
WHERE p.IsActive = True
GROUP BY p.ProductID, p.ProductName, c.CategoryName, p.ProductionTime, p.IsActive
HAVING COUNT(po.OrderID) > 0
ORDER BY [نسبة الإنجاز %] DESC;
