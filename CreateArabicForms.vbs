' إنشاء النماذج العربية في Microsoft Access
' يجب تشغيل هذا السكريپت من خارج Access

Dim accessApp
Set accessApp = CreateObject("Access.Application")

' فتح قاعدة البيانات
Dim dbPath
dbPath = CreateObject("Scripting.FileSystemObject").GetAbsolutePathName("Factory_Accounting_System.accdb")
accessApp.OpenCurrentDatabase dbPath

WScript.Echo "تم فتح قاعدة البيانات بنجاح"

' إنشاء النموذج الرئيسي للتنقل
CreateMainNavigationForm accessApp

' إنشاء نموذج الموردين
CreateSuppliersForm accessApp

' إنشاء نموذج المواد الخام  
CreateRawMaterialsForm accessApp

' إنشاء نموذج فواتير الشراء
CreatePurchaseInvoicesForm accessApp

WScript.Echo "تم إنشاء جميع النماذج العربية بنجاح!"

' إغلاق Access
accessApp.Quit
Set accessApp = Nothing

' دالة إنشاء النموذج الرئيسي
Sub CreateMainNavigationForm(app)
    On Error Resume Next
    
    ' حذف النموذج إذا كان موجوداً
    app.DoCmd.DeleteObject 4, "القائمة_الرئيسية" ' 4 = acForm
    
    ' إنشاء نموذج جديد
    Dim sql
    sql = "CREATE FORM القائمة_الرئيسية"
    
    ' تشغيل الأمر
    app.DoCmd.RunSQL sql
    
    WScript.Echo "تم إنشاء النموذج الرئيسي"
End Sub

' دالة إنشاء نموذج الموردين
Sub CreateSuppliersForm(app)
    On Error Resume Next
    
    ' حذف النموذج إذا كان موجوداً
    app.DoCmd.DeleteObject 4, "نموذج_الموردين"
    
    ' إنشاء نموذج بسيط للموردين
    Dim formName
    formName = "نموذج_الموردين"
    
    ' إنشاء النموذج باستخدام معالج النماذج
    app.DoCmd.OpenForm "Suppliers", 0, , , , 1 ' Design View
    app.DoCmd.Save 4, formName
    app.DoCmd.Close 4, "Suppliers"
    
    WScript.Echo "تم إنشاء نموذج الموردين"
End Sub

' دالة إنشاء نموذج المواد الخام
Sub CreateRawMaterialsForm(app)
    On Error Resume Next
    
    ' حذف النموذج إذا كان موجوداً
    app.DoCmd.DeleteObject 4, "نموذج_المواد_الخام"
    
    ' إنشاء نموذج بسيط للمواد الخام
    Dim formName
    formName = "نموذج_المواد_الخام"
    
    ' إنشاء النموذج باستخدام معالج النماذج
    app.DoCmd.OpenForm "RawMaterials", 0, , , , 1 ' Design View
    app.DoCmd.Save 4, formName
    app.DoCmd.Close 4, "RawMaterials"
    
    WScript.Echo "تم إنشاء نموذج المواد الخام"
End Sub

' دالة إنشاء نموذج فواتير الشراء
Sub CreatePurchaseInvoicesForm(app)
    On Error Resume Next
    
    ' حذف النموذج إذا كان موجوداً
    app.DoCmd.DeleteObject 4, "نموذج_فواتير_الشراء"
    
    ' إنشاء نموذج بسيط لفواتير الشراء
    Dim formName
    formName = "نموذج_فواتير_الشراء"
    
    ' إنشاء النموذج باستخدام معالج النماذج
    app.DoCmd.OpenForm "PurchaseInvoices", 0, , , , 1 ' Design View
    app.DoCmd.Save 4, formName
    app.DoCmd.Close 4, "PurchaseInvoices"
    
    WScript.Echo "تم إنشاء نموذج فواتير الشراء"
End Sub
