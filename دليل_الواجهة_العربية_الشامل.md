# دليل الواجهة العربية الشامل لنظام محاسبة مصنع المعمول والمخللات

## 📋 ملخص تنفيذي

تم تطوير واجهة عربية متكاملة لنظام محاسبة مصنع المعمول والمخللات في Microsoft Access. هذا الدليل يوضح جميع المكونات المطورة وكيفية استخدامها وتخصيصها.

## 🎯 المكونات المطورة

### 1. الاستعلامات العربية (Arabic Queries)

تم إنشاء 6 استعلامات رئيسية مع أسماء حقول عربية:

#### أ. تقرير المخزون الشامل (InventoryReport_AR)
```sql
SELECT 
    rm.MaterialName AS [اسم المادة],
    c.CategoryName AS [الفئة],
    u.UnitName AS [وحدة القياس],
    IIF(inv.AvailableQuantity IS NULL, 0, inv.AvailableQuantity) AS [الكمية المتاحة],
    IIF(inv.AverageCost IS NULL, 0, inv.AverageCost) AS [متوسط التكلفة],
    IIF(inv.TotalValue IS NULL, 0, inv.TotalValue) AS [إجمالي القيمة],
    rm.MinimumLevel AS [الحد الأدنى],
    IIF(IIF(inv.AvailableQuantity IS NULL, 0, inv.AvailableQuantity) <= rm.MinimumLevel, "تحت الحد الأدنى", "مناسب") AS [حالة المخزون]
FROM ((RawMaterials rm 
LEFT JOIN Categories c ON rm.CategoryID = c.CategoryID) 
LEFT JOIN Units u ON rm.UnitID = u.UnitID) 
LEFT JOIN RawMaterialsInventory inv ON rm.MaterialID = inv.MaterialID
```

#### ب. تقرير تكاليف الإنتاج (ProductionCostReport_AR)
```sql
SELECT 
    po.OrderID AS [رقم أمر الإنتاج],
    p.ProductName AS [اسم المنتج],
    c.CategoryName AS [فئة المنتج],
    po.RequiredQuantity AS [الكمية المطلوبة],
    po.ProducedQuantity AS [الكمية المنتجة],
    IIF(po.RequiredQuantity > 0, (po.ProducedQuantity / po.RequiredQuantity) * 100, 0) AS [نسبة الإنجاز %],
    po.EstimatedCost AS [التكلفة المقدرة],
    IIF(po.ActualCost IS NULL, 0, po.ActualCost) AS [التكلفة الفعلية]
FROM (ProductionOrders po 
LEFT JOIN Products p ON po.ProductID = p.ProductID)
LEFT JOIN Categories c ON p.CategoryID = c.CategoryID
```

#### ج. تقرير الفواتير المستحقة (DueInvoicesReport_AR)
```sql
SELECT 
    pi.InvoiceID AS [رقم الفاتورة],
    pi.SupplierInvoiceNumber AS [رقم فاتورة المورد],
    s.SupplierName AS [اسم المورد],
    s.ContactPerson AS [جهة الاتصال],
    pi.InvoiceDate AS [تاريخ الفاتورة],
    pi.DueDate AS [تاريخ الاستحقاق],
    pi.TotalAmount AS [إجمالي الفاتورة],
    pi.RemainingAmount AS [المبلغ المتبقي],
    DateDiff("d", pi.DueDate, Date()) AS [أيام التأخير]
FROM (PurchaseInvoices pi 
LEFT JOIN Suppliers s ON pi.SupplierID = s.SupplierID)
WHERE pi.InvoiceStatus = 'Open' AND pi.RemainingAmount > 0
```

#### د. تقرير تحليل الأرباح (ProfitabilityReport_AR)
```sql
SELECT 
    p.ProductName AS [اسم المنتج],
    c.CategoryName AS [فئة المنتج],
    p.SalePrice AS [سعر البيع],
    p.ProductionCost AS [تكلفة الإنتاج],
    (p.SalePrice - p.ProductionCost) AS [هامش الربح],
    IIF(p.ProductionCost > 0, ((p.SalePrice - p.ProductionCost) / p.ProductionCost) * 100, 0) AS [نسبة الربح %]
FROM ((Products p 
LEFT JOIN Categories c ON p.CategoryID = c.CategoryID)
LEFT JOIN Units u ON p.UnitID = u.UnitID)
WHERE p.IsActive = True
```

#### هـ. تقرير حركات المخزون (InventoryMovementsReport_AR)
```sql
SELECT 
    im.MovementDate AS [تاريخ الحركة],
    IIF(im.MovementType = 'Purchase', 'شراء', IIF(im.MovementType = 'Production', 'إنتاج', 'بيع')) AS [نوع الحركة],
    IIF(im.ItemType = 'Raw Material', 'مادة خام', 'منتج نهائي') AS [نوع المادة],
    im.Quantity AS [الكمية],
    im.UnitPrice AS [سعر الوحدة],
    im.TotalValue AS [إجمالي القيمة]
FROM InventoryMovements im
```

#### و. تقرير الموردين (SuppliersReport_AR)
```sql
SELECT 
    s.SupplierName AS [اسم المورد],
    s.ContactPerson AS [جهة الاتصال],
    s.Phone AS [الهاتف],
    s.Email AS [البريد الإلكتروني],
    s.CreditLimit AS [حد الائتمان],
    s.AccountBalance AS [رصيد الحساب],
    COUNT(pi.InvoiceID) AS [عدد الفواتير],
    IIF(SUM(pi.TotalAmount) IS NULL, 0, SUM(pi.TotalAmount)) AS [إجمالي المشتريات]
FROM Suppliers s 
LEFT JOIN PurchaseInvoices pi ON s.SupplierID = pi.SupplierID
GROUP BY s.SupplierID, s.SupplierName, s.ContactPerson, s.Phone, s.Email, s.CreditLimit, s.AccountBalance
```

### 2. النماذج العربية (Arabic Forms)

#### أ. النموذج الرئيسي للتنقل (القائمة_الرئيسية)
- **الغرض**: قائمة تنقل رئيسية بواجهة عربية كاملة
- **الخصائص**:
  - RightToLeft = True
  - RightToLeftLayout = True
  - Caption = "نظام محاسبة مصنع المعمول والمخللات - القائمة الرئيسية"
- **الأقسام**:
  - إدارة البيانات الأساسية
  - العمليات اليومية
  - التقارير والاستعلامات

#### ب. نموذج الموردين (نموذج_الموردين)
- **الحقول العربية**:
  - اسم المورد
  - جهة الاتصال
  - الهاتف والجوال
  - البريد الإلكتروني
  - المدينة والدولة
  - شروط الدفع
  - حد الائتمان
  - رصيد الحساب

#### ج. نموذج المواد الخام (نموذج_المواد_الخام)
- **الحقول العربية**:
  - اسم المادة
  - الفئة
  - وحدة القياس
  - الحد الأدنى
  - متوسط السعر

#### د. نموذج فواتير الشراء (نموذج_فواتير_الشراء)
- **الحقول العربية**:
  - رقم فاتورة المورد
  - تاريخ الفاتورة
  - المورد
  - العملة
  - إجمالي الفاتورة
  - المبلغ المدفوع
  - حالة الفاتورة

### 3. التقارير العربية (Arabic Reports)

#### أ. تقرير المخزون الشامل (تقرير_المخزون_الشامل)
- **الخصائص**:
  - RightToLeft = True
  - RightToLeftLayout = True
  - عنوان: "تقرير المخزون الشامل"
  - عنوان فرعي: "مصنع المعمول والمخللات"
- **الأعمدة**:
  - اسم المادة
  - الفئة
  - وحدة القياس
  - الكمية المتاحة
  - متوسط التكلفة
  - إجمالي القيمة
  - حالة المخزون

#### ب. تقرير الموردين والأرصدة (تقرير_الموردين_والأرصدة)
- **الخصائص**:
  - تنسيق RTL
  - رؤوس أعمدة عربية
  - تنسيق العملة بالريال
- **الأعمدة**:
  - اسم المورد
  - جهة الاتصال
  - الهاتف
  - حد الائتمان
  - رصيد الحساب
  - إجمالي المشتريات

### 4. الإعدادات التقنية للعربية

#### أ. جدول إعدادات النظام (SystemSettings)
```sql
CREATE TABLE SystemSettings (
    SettingID AUTOINCREMENT PRIMARY KEY,
    SettingName TEXT(100) NOT NULL,
    SettingValue TEXT(255),
    SettingDescription TEXT(500),
    SettingCategory TEXT(50),
    IsActive YESNO DEFAULT True,
    CreatedDate DATETIME DEFAULT Now()
)
```

#### ب. الإعدادات الأساسية المدرجة:
- **DefaultLanguage**: Arabic
- **DateFormat**: dd/mm/yyyy
- **CurrencyFormat**: SAR
- **TextDirection**: RTL
- **FontName**: Tahoma
- **FontSize**: 11

#### ج. جدول إعدادات المستخدم (UserSettings)
```sql
CREATE TABLE UserSettings (
    UserSettingID AUTOINCREMENT PRIMARY KEY,
    UserName TEXT(50) NOT NULL,
    LanguagePreference TEXT(20) DEFAULT 'Arabic',
    DateFormat TEXT(20) DEFAULT 'dd/mm/yyyy',
    FontName TEXT(50) DEFAULT 'Tahoma',
    FontSize INTEGER DEFAULT 11,
    IsActive YESNO DEFAULT True
)
```

## 🔧 خطوات التنفيذ

### الخطوة 1: إنشاء الاستعلامات
1. افتح Microsoft Access
2. انتقل إلى Create > Query Design
3. انسخ كود SQL للاستعلام المطلوب
4. احفظ الاستعلام بالاسم العربي المحدد

### الخطوة 2: إنشاء النماذج
1. استخدم كود VBA المرفق في ملف "كود_النماذج_العربية.txt"
2. انسخ الكود في وحدة VBA جديدة
3. شغل الدالة CreateAllArabicForms()
4. اضبط خصائص النماذج يدوياً:
   - RightToLeft = True
   - RightToLeftLayout = True
   - Font = Tahoma

### الخطوة 3: إنشاء التقارير
1. استخدم كود VBA المرفق في ملف "كود_التقارير_العربية.txt"
2. انسخ الكود في وحدة VBA
3. شغل الدالة CreateAllArabicReports()
4. اضبط تنسيق التقارير للطباعة

### الخطوة 4: ضبط الإعدادات
1. شغل سكريپت CreateArabicSupport.ps1
2. تحقق من إنشاء الجداول الداعمة
3. اضبط إعدادات Windows للعربية

## ✅ قائمة التحقق

### الاستعلامات:
- [x] InventoryReport_AR - تقرير المخزون الشامل
- [x] ProductionCostReport_AR - تقرير تكاليف الإنتاج
- [x] DueInvoicesReport_AR - تقرير الفواتير المستحقة
- [x] ProfitabilityReport_AR - تقرير تحليل الأرباح
- [x] InventoryMovementsReport_AR - تقرير حركات المخزون
- [x] SuppliersReport_AR - تقرير الموردين

### النماذج:
- [x] القائمة_الرئيسية - النموذج الرئيسي
- [x] نموذج_الموردين - إدارة الموردين
- [x] نموذج_المواد_الخام - إدارة المواد الخام
- [x] نموذج_فواتير_الشراء - فواتير الشراء

### التقارير:
- [x] تقرير_المخزون_الشامل
- [x] تقرير_الموردين_والأرصدة

### الإعدادات:
- [x] SystemSettings - جدول إعدادات النظام
- [x] UserSettings - جدول إعدادات المستخدم
- [x] ActivityLog - جدول سجل الأنشطة

## 🚀 التشغيل والاستخدام

### 1. فتح النظام:
- افتح ملف Factory_Accounting_System.accdb
- ستجد جميع المكونات العربية في قوائم Access

### 2. استخدام النماذج:
- افتح "القائمة_الرئيسية" للتنقل السريع
- استخدم النماذج المختلفة لإدخال البيانات
- جميع النماذج تدعم الكتابة بالعربية

### 3. عرض التقارير:
- افتح التقارير من قائمة Reports
- جميع التقارير مُعدة للطباعة بتنسيق RTL
- يمكن تصدير التقارير إلى PDF أو Excel

### 4. استخدام الاستعلامات:
- افتح الاستعلامات من قائمة Queries
- جميع النتائج تظهر بأسماء حقول عربية
- يمكن استخدام الاستعلامات في تقارير جديدة

## 🔍 اختبار الواجهة العربية

### اختبار النصوص:
1. أدخل نصوص عربية في النماذج
2. تحقق من عرض النصوص بشكل صحيح
3. اطبع التقارير وتأكد من التنسيق

### اختبار الوظائف:
1. اختبر جميع الأزرار والقوائم
2. تحقق من عمل الحسابات التلقائية
3. اختبر البحث والفلترة

### اختبار الأداء:
1. اختبر النظام مع بيانات كبيرة
2. تحقق من سرعة الاستجابة
3. اختبر الطباعة والتصدير

## 📞 الدعم والصيانة

### المشاكل الشائعة:
1. **عدم ظهور النصوص العربية**: تأكد من ضبط خط Tahoma
2. **اتجاه النص خاطئ**: اضبط RightToLeft = True
3. **مشاكل الطباعة**: تحقق من إعدادات الطابعة

### التحديثات المستقبلية:
1. إضافة المزيد من التقارير
2. تطوير واجهة ويب عربية
3. إضافة دعم لغات أخرى

---

**تم إنجاز الواجهة العربية بنجاح!** 🎉

جميع المكونات جاهزة للاستخدام الفوري مع دعم كامل للغة العربية.
