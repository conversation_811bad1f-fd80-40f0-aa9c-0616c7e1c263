كود VBA لإنشاء التقارير العربية في Microsoft Access
=====================================================

يجب نسخ هذا الكود في وحدة VBA جديدة في Access:

' إنشاء جميع التقارير العربية
Sub CreateAllArabicReports()
    ' إنشاء التقارير الأساسية
    CreateInventoryReportArabic
    CreateSuppliersReportArabic
    CreateProfitabilityReportArabic
    CreateProductionCostReportArabic
    CreateDueInvoicesReportArabic
    CreateInventoryMovementsReportArabic
    
    MsgBox "تم إنشاء جميع التقارير العربية بنجاح!", vbInformation, "نظام محاسبة المصنع"
End Sub

' تقرير المخزون الشامل
Sub CreateInventoryReportArabic()
    Dim rpt As Report
    Set rpt = CreateReport()
    
    ' خصائص التقرير الأساسية
    rpt.Caption = "تقرير المخزون الشامل - نظام محاسبة مصنع المعمول والمخللات"
    rpt.RecordSource = "InventoryReport_AR"
    rpt.RightToLeft = True
    rpt.RightToLeftLayout = True
    
    ' إعداد الصفحة
    rpt.Width = 11000 ' A4 width
    rpt.Orientation = 1 ' Portrait
    
    ' إضافة رأس التقرير
    Dim ctl As Control
    Dim intTop As Integer
    intTop = 0
    
    ' عنوان التقرير الرئيسي
    Set ctl = CreateControl(rpt.Name, acLabel, acPageHeader, , , 500, intTop, 10000, 600)
    ctl.Caption = "تقرير المخزون الشامل"
    ctl.FontSize = 18
    ctl.FontBold = True
    ctl.TextAlign = 2 ' Center
    ctl.BackColor = RGB(0, 120, 215)
    ctl.ForeColor = RGB(255, 255, 255)
    intTop = intTop + 700
    
    ' عنوان فرعي
    Set ctl = CreateControl(rpt.Name, acLabel, acPageHeader, , , 500, intTop, 10000, 400)
    ctl.Caption = "مصنع المعمول والمخللات"
    ctl.FontSize = 12
    ctl.FontBold = True
    ctl.TextAlign = 2 ' Center
    intTop = intTop + 500
    
    ' تاريخ التقرير
    Set ctl = CreateControl(rpt.Name, acLabel, acPageHeader, , , 500, intTop, 5000, 300)
    ctl.Caption = "تاريخ التقرير: " & Format(Date, "dd/mm/yyyy")
    ctl.FontSize = 10
    ctl.FontBold = True
    
    ' رقم الصفحة
    Set ctl = CreateControl(rpt.Name, acLabel, acPageHeader, , , 5500, intTop, 5000, 300)
    ctl.Caption = "صفحة: " & "[Page] من [Pages]"
    ctl.FontSize = 10
    ctl.FontBold = True
    ctl.TextAlign = 3 ' Right
    intTop = intTop + 400
    
    ' رؤوس الأعمدة
    Dim colWidth As Integer
    colWidth = 1400
    Dim colLeft As Integer
    colLeft = 500
    
    ' اسم المادة
    Set ctl = CreateControl(rpt.Name, acLabel, acPageHeader, , , colLeft, intTop, colWidth, 400)
    ctl.Caption = "اسم المادة"
    ctl.FontBold = True
    ctl.BackColor = RGB(220, 220, 220)
    ctl.BorderStyle = 1
    colLeft = colLeft + colWidth
    
    ' الفئة
    Set ctl = CreateControl(rpt.Name, acLabel, acPageHeader, , , colLeft, intTop, colWidth, 400)
    ctl.Caption = "الفئة"
    ctl.FontBold = True
    ctl.BackColor = RGB(220, 220, 220)
    ctl.BorderStyle = 1
    colLeft = colLeft + colWidth
    
    ' وحدة القياس
    Set ctl = CreateControl(rpt.Name, acLabel, acPageHeader, , , colLeft, intTop, colWidth, 400)
    ctl.Caption = "وحدة القياس"
    ctl.FontBold = True
    ctl.BackColor = RGB(220, 220, 220)
    ctl.BorderStyle = 1
    colLeft = colLeft + colWidth
    
    ' الكمية المتاحة
    Set ctl = CreateControl(rpt.Name, acLabel, acPageHeader, , , colLeft, intTop, colWidth, 400)
    ctl.Caption = "الكمية المتاحة"
    ctl.FontBold = True
    ctl.BackColor = RGB(220, 220, 220)
    ctl.BorderStyle = 1
    colLeft = colLeft + colWidth
    
    ' متوسط التكلفة
    Set ctl = CreateControl(rpt.Name, acLabel, acPageHeader, , , colLeft, intTop, colWidth, 400)
    ctl.Caption = "متوسط التكلفة"
    ctl.FontBold = True
    ctl.BackColor = RGB(220, 220, 220)
    ctl.BorderStyle = 1
    colLeft = colLeft + colWidth
    
    ' إجمالي القيمة
    Set ctl = CreateControl(rpt.Name, acLabel, acPageHeader, , , colLeft, intTop, colWidth, 400)
    ctl.Caption = "إجمالي القيمة"
    ctl.FontBold = True
    ctl.BackColor = RGB(220, 220, 220)
    ctl.BorderStyle = 1
    colLeft = colLeft + colWidth
    
    ' حالة المخزون
    Set ctl = CreateControl(rpt.Name, acLabel, acPageHeader, , , colLeft, intTop, colWidth, 400)
    ctl.Caption = "حالة المخزون"
    ctl.FontBold = True
    ctl.BackColor = RGB(220, 220, 220)
    ctl.BorderStyle = 1
    
    ' إضافة بيانات التفاصيل
    intTop = 0
    colLeft = 500
    
    ' اسم المادة
    Set ctl = CreateControl(rpt.Name, acTextBox, acDetail, , "اسم المادة", colLeft, intTop, colWidth, 300)
    ctl.BorderStyle = 1
    colLeft = colLeft + colWidth
    
    ' الفئة
    Set ctl = CreateControl(rpt.Name, acTextBox, acDetail, , "الفئة", colLeft, intTop, colWidth, 300)
    ctl.BorderStyle = 1
    colLeft = colLeft + colWidth
    
    ' وحدة القياس
    Set ctl = CreateControl(rpt.Name, acTextBox, acDetail, , "وحدة القياس", colLeft, intTop, colWidth, 300)
    ctl.BorderStyle = 1
    colLeft = colLeft + colWidth
    
    ' الكمية المتاحة
    Set ctl = CreateControl(rpt.Name, acTextBox, acDetail, , "الكمية المتاحة", colLeft, intTop, colWidth, 300)
    ctl.BorderStyle = 1
    ctl.Format = "Standard"
    ctl.TextAlign = 3 ' Right
    colLeft = colLeft + colWidth
    
    ' متوسط التكلفة
    Set ctl = CreateControl(rpt.Name, acTextBox, acDetail, , "متوسط التكلفة", colLeft, intTop, colWidth, 300)
    ctl.BorderStyle = 1
    ctl.Format = "Currency"
    ctl.TextAlign = 3 ' Right
    colLeft = colLeft + colWidth
    
    ' إجمالي القيمة
    Set ctl = CreateControl(rpt.Name, acTextBox, acDetail, , "إجمالي القيمة", colLeft, intTop, colWidth, 300)
    ctl.BorderStyle = 1
    ctl.Format = "Currency"
    ctl.TextAlign = 3 ' Right
    colLeft = colLeft + colWidth
    
    ' حالة المخزون
    Set ctl = CreateControl(rpt.Name, acTextBox, acDetail, , "حالة المخزون", colLeft, intTop, colWidth, 300)
    ctl.BorderStyle = 1
    ctl.TextAlign = 2 ' Center
    
    ' إضافة تذييل التقرير
    Set ctl = CreateControl(rpt.Name, acLabel, acReportFooter, , , 500, 0, 10000, 400)
    ctl.Caption = "انتهى التقرير - تم الإنشاء بواسطة نظام محاسبة مصنع المعمول والمخللات"
    ctl.FontSize = 10
    ctl.FontBold = True
    ctl.TextAlign = 2 ' Center
    ctl.BackColor = RGB(240, 240, 240)
    
    ' حفظ التقرير
    DoCmd.Save acReport, rpt.Name
    DoCmd.Rename "تقرير_المخزون_الشامل", acReport, rpt.Name
End Sub

' تقرير الموردين والأرصدة
Sub CreateSuppliersReportArabic()
    Dim rpt As Report
    Set rpt = CreateReport()
    
    ' خصائص التقرير الأساسية
    rpt.Caption = "تقرير الموردين والأرصدة - نظام محاسبة مصنع المعمول والمخللات"
    rpt.RecordSource = "SuppliersReport_AR"
    rpt.RightToLeft = True
    rpt.RightToLeftLayout = True
    
    ' إعداد الصفحة
    rpt.Width = 11000 ' A4 width
    rpt.Orientation = 1 ' Portrait
    
    ' إضافة رأس التقرير
    Dim ctl As Control
    Dim intTop As Integer
    intTop = 0
    
    ' عنوان التقرير الرئيسي
    Set ctl = CreateControl(rpt.Name, acLabel, acPageHeader, , , 500, intTop, 10000, 600)
    ctl.Caption = "تقرير الموردين والأرصدة"
    ctl.FontSize = 18
    ctl.FontBold = True
    ctl.TextAlign = 2 ' Center
    ctl.BackColor = RGB(0, 120, 215)
    ctl.ForeColor = RGB(255, 255, 255)
    intTop = intTop + 700
    
    ' عنوان فرعي
    Set ctl = CreateControl(rpt.Name, acLabel, acPageHeader, , , 500, intTop, 10000, 400)
    ctl.Caption = "مصنع المعمول والمخللات"
    ctl.FontSize = 12
    ctl.FontBold = True
    ctl.TextAlign = 2 ' Center
    intTop = intTop + 500
    
    ' تاريخ التقرير
    Set ctl = CreateControl(rpt.Name, acLabel, acPageHeader, , , 500, intTop, 5000, 300)
    ctl.Caption = "تاريخ التقرير: " & Format(Date, "dd/mm/yyyy")
    ctl.FontSize = 10
    ctl.FontBold = True
    
    ' رقم الصفحة
    Set ctl = CreateControl(rpt.Name, acLabel, acPageHeader, , , 5500, intTop, 5000, 300)
    ctl.Caption = "صفحة: " & "[Page] من [Pages]"
    ctl.FontSize = 10
    ctl.FontBold = True
    ctl.TextAlign = 3 ' Right
    intTop = intTop + 400
    
    ' رؤوس الأعمدة
    Dim colWidth As Integer
    colWidth = 1600
    Dim colLeft As Integer
    colLeft = 500
    
    ' اسم المورد
    Set ctl = CreateControl(rpt.Name, acLabel, acPageHeader, , , colLeft, intTop, colWidth, 400)
    ctl.Caption = "اسم المورد"
    ctl.FontBold = True
    ctl.BackColor = RGB(220, 220, 220)
    ctl.BorderStyle = 1
    colLeft = colLeft + colWidth
    
    ' جهة الاتصال
    Set ctl = CreateControl(rpt.Name, acLabel, acPageHeader, , , colLeft, intTop, colWidth, 400)
    ctl.Caption = "جهة الاتصال"
    ctl.FontBold = True
    ctl.BackColor = RGB(220, 220, 220)
    ctl.BorderStyle = 1
    colLeft = colLeft + colWidth
    
    ' الهاتف
    Set ctl = CreateControl(rpt.Name, acLabel, acPageHeader, , , colLeft, intTop, colWidth, 400)
    ctl.Caption = "الهاتف"
    ctl.FontBold = True
    ctl.BackColor = RGB(220, 220, 220)
    ctl.BorderStyle = 1
    colLeft = colLeft + colWidth
    
    ' حد الائتمان
    Set ctl = CreateControl(rpt.Name, acLabel, acPageHeader, , , colLeft, intTop, colWidth, 400)
    ctl.Caption = "حد الائتمان"
    ctl.FontBold = True
    ctl.BackColor = RGB(220, 220, 220)
    ctl.BorderStyle = 1
    colLeft = colLeft + colWidth
    
    ' رصيد الحساب
    Set ctl = CreateControl(rpt.Name, acLabel, acPageHeader, , , colLeft, intTop, colWidth, 400)
    ctl.Caption = "رصيد الحساب"
    ctl.FontBold = True
    ctl.BackColor = RGB(220, 220, 220)
    ctl.BorderStyle = 1
    colLeft = colLeft + colWidth
    
    ' إجمالي المشتريات
    Set ctl = CreateControl(rpt.Name, acLabel, acPageHeader, , , colLeft, intTop, colWidth, 400)
    ctl.Caption = "إجمالي المشتريات"
    ctl.FontBold = True
    ctl.BackColor = RGB(220, 220, 220)
    ctl.BorderStyle = 1
    
    ' إضافة بيانات التفاصيل
    intTop = 0
    colLeft = 500
    
    ' اسم المورد
    Set ctl = CreateControl(rpt.Name, acTextBox, acDetail, , "اسم المورد", colLeft, intTop, colWidth, 300)
    ctl.BorderStyle = 1
    colLeft = colLeft + colWidth
    
    ' جهة الاتصال
    Set ctl = CreateControl(rpt.Name, acTextBox, acDetail, , "جهة الاتصال", colLeft, intTop, colWidth, 300)
    ctl.BorderStyle = 1
    colLeft = colLeft + colWidth
    
    ' الهاتف
    Set ctl = CreateControl(rpt.Name, acTextBox, acDetail, , "الهاتف", colLeft, intTop, colWidth, 300)
    ctl.BorderStyle = 1
    colLeft = colLeft + colWidth
    
    ' حد الائتمان
    Set ctl = CreateControl(rpt.Name, acTextBox, acDetail, , "حد الائتمان", colLeft, intTop, colWidth, 300)
    ctl.BorderStyle = 1
    ctl.Format = "Currency"
    ctl.TextAlign = 3 ' Right
    colLeft = colLeft + colWidth
    
    ' رصيد الحساب
    Set ctl = CreateControl(rpt.Name, acTextBox, acDetail, , "رصيد الحساب", colLeft, intTop, colWidth, 300)
    ctl.BorderStyle = 1
    ctl.Format = "Currency"
    ctl.TextAlign = 3 ' Right
    colLeft = colLeft + colWidth
    
    ' إجمالي المشتريات
    Set ctl = CreateControl(rpt.Name, acTextBox, acDetail, , "إجمالي المشتريات", colLeft, intTop, colWidth, 300)
    ctl.BorderStyle = 1
    ctl.Format = "Currency"
    ctl.TextAlign = 3 ' Right
    
    ' إضافة تذييل التقرير
    Set ctl = CreateControl(rpt.Name, acLabel, acReportFooter, , , 500, 0, 10000, 400)
    ctl.Caption = "انتهى التقرير - تم الإنشاء بواسطة نظام محاسبة مصنع المعمول والمخللات"
    ctl.FontSize = 10
    ctl.FontBold = True
    ctl.TextAlign = 2 ' Center
    ctl.BackColor = RGB(240, 240, 240)
    
    ' حفظ التقرير
    DoCmd.Save acReport, rpt.Name
    DoCmd.Rename "تقرير_الموردين_والأرصدة", acReport, rpt.Name
End Sub
