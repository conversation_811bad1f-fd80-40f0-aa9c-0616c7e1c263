-- استعلامات جاهزة للنسخ واللصق في Microsoft Access
-- ========================================================

-- 1. استعلام المواد الخام العربي
-- ===============================
-- اسم الاستعلام: استعلام_المواد_الخام
SELECT 
    rm.MaterialName AS [اسم المادة],
    c.CategoryName AS [الفئة],
    u.UnitName AS [وحدة القياس],
    rm.MinimumLevel AS [الحد الأدنى],
    rm.AveragePrice AS [متوسط السعر],
    IIF(rm.IsActive, "نشط", "غير نشط") AS [الحالة]
FROM (RawMaterials rm 
LEFT JOIN Categories c ON rm.CategoryID = c.CategoryID) 
LEFT JOIN Units u ON rm.UnitID = u.UnitID
ORDER BY c.CategoryName, rm.MaterialName;

-- 2. استعلام الموردين العربي
-- ===========================
-- اسم الاستعلام: استعلام_الموردين
SELECT 
    s.SupplierName AS [اسم المورد],
    s.ContactPerson AS [جهة الاتصال],
    s.Phone AS [الهاتف],
    s.Mobile AS [الجوال],
    s.Email AS [البريد الإلكتروني],
    s.City AS [المدينة],
    s.Country AS [الدولة],
    s.PaymentTerms AS [شروط الدفع],
    s.CreditLimit AS [حد الائتمان],
    s.AccountBalance AS [رصيد الحساب],
    s.RegistrationDate AS [تاريخ التسجيل],
    IIF(s.IsActive, "نشط", "غير نشط") AS [الحالة]
FROM Suppliers s
ORDER BY s.SupplierName;

-- 3. استعلام المنتجات العربي
-- ===========================
-- اسم الاستعلام: استعلام_المنتجات
SELECT 
    p.ProductName AS [اسم المنتج],
    c.CategoryName AS [فئة المنتج],
    u.UnitName AS [وحدة القياس],
    p.SalePrice AS [سعر البيع],
    p.ProductionCost AS [تكلفة الإنتاج],
    (p.SalePrice - p.ProductionCost) AS [هامش الربح],
    IIF(p.ProductionCost > 0, ((p.SalePrice - p.ProductionCost) / p.ProductionCost) * 100, 0) AS [نسبة الربح %],
    p.MinimumLevel AS [الحد الأدنى],
    p.ProductionTime AS [وقت الإنتاج بالدقائق],
    IIF(p.IsActive, "نشط", "غير نشط") AS [الحالة]
FROM (Products p 
LEFT JOIN Categories c ON p.CategoryID = c.CategoryID)
LEFT JOIN Units u ON p.UnitID = u.UnitID
ORDER BY c.CategoryName, p.ProductName;

-- 4. استعلام فواتير الشراء العربي
-- =================================
-- اسم الاستعلام: استعلام_فواتير_الشراء
SELECT 
    pi.InvoiceID AS [رقم الفاتورة],
    pi.SupplierInvoiceNumber AS [رقم فاتورة المورد],
    s.SupplierName AS [اسم المورد],
    pi.InvoiceDate AS [تاريخ الفاتورة],
    pi.DueDate AS [تاريخ الاستحقاق],
    cur.CurrencyName AS [العملة],
    pi.ExchangeRate AS [سعر الصرف],
    pi.SubTotal AS [إجمالي قبل الضريبة],
    pi.TaxAmount AS [قيمة الضريبة],
    pi.TotalAmount AS [إجمالي الفاتورة],
    pi.PaidAmount AS [المبلغ المدفوع],
    pi.RemainingAmount AS [المبلغ المتبقي],
    IIF(pi.InvoiceStatus = 'Open', 'مفتوحة', 
        IIF(pi.InvoiceStatus = 'Paid', 'مدفوعة', 
            IIF(pi.InvoiceStatus = 'Cancelled', 'ملغاة', pi.InvoiceStatus))) AS [حالة الفاتورة]
FROM (PurchaseInvoices pi 
LEFT JOIN Suppliers s ON pi.SupplierID = s.SupplierID)
LEFT JOIN Currencies cur ON pi.CurrencyID = cur.CurrencyID
ORDER BY pi.InvoiceDate DESC;

-- 5. استعلام أوامر الإنتاج العربي
-- ===============================
-- اسم الاستعلام: استعلام_أوامر_الإنتاج
SELECT 
    po.OrderID AS [رقم أمر الإنتاج],
    p.ProductName AS [اسم المنتج],
    po.RequiredQuantity AS [الكمية المطلوبة],
    po.ProducedQuantity AS [الكمية المنتجة],
    IIF(po.RequiredQuantity > 0, (po.ProducedQuantity / po.RequiredQuantity) * 100, 0) AS [نسبة الإنجاز %],
    po.OrderDate AS [تاريخ الأمر],
    po.StartDate AS [تاريخ البدء],
    po.EndDate AS [تاريخ الانتهاء],
    po.EstimatedCost AS [التكلفة المقدرة],
    po.ActualCost AS [التكلفة الفعلية],
    IIF(po.OrderStatus = 'New', 'جديد',
        IIF(po.OrderStatus = 'In Progress', 'قيد التنفيذ',
            IIF(po.OrderStatus = 'Complete', 'مكتمل',
                IIF(po.OrderStatus = 'Cancelled', 'ملغي', po.OrderStatus)))) AS [حالة الأمر],
    po.EntryUser AS [المستخدم المسؤول]
FROM ProductionOrders po 
LEFT JOIN Products p ON po.ProductID = p.ProductID
ORDER BY po.OrderDate DESC;

-- 6. استعلام المخزون الشامل العربي
-- =================================
-- اسم الاستعلام: استعلام_المخزون_الشامل
SELECT 
    rm.MaterialName AS [اسم المادة],
    c.CategoryName AS [الفئة],
    u.UnitName AS [وحدة القياس],
    IIF(inv.AvailableQuantity IS NULL, 0, inv.AvailableQuantity) AS [الكمية المتاحة],
    IIF(inv.AverageCost IS NULL, 0, inv.AverageCost) AS [متوسط التكلفة],
    IIF(inv.TotalValue IS NULL, 0, inv.TotalValue) AS [إجمالي القيمة],
    rm.MinimumLevel AS [الحد الأدنى],
    IIF(IIF(inv.AvailableQuantity IS NULL, 0, inv.AvailableQuantity) <= rm.MinimumLevel, "تحت الحد الأدنى", "مناسب") AS [حالة المخزون],
    inv.LastMovementDate AS [تاريخ آخر حركة],
    rm.AveragePrice AS [متوسط سعر الشراء],
    IIF(rm.IsActive, "نشط", "غير نشط") AS [الحالة]
FROM ((RawMaterials rm 
LEFT JOIN Categories c ON rm.CategoryID = c.CategoryID) 
LEFT JOIN Units u ON rm.UnitID = u.UnitID) 
LEFT JOIN RawMaterialsInventory inv ON rm.MaterialID = inv.MaterialID
ORDER BY c.CategoryName, rm.MaterialName;

-- 7. استعلام الفواتير المستحقة العربي
-- ===================================
-- اسم الاستعلام: استعلام_الفواتير_المستحقة
SELECT 
    pi.InvoiceID AS [رقم الفاتورة],
    pi.SupplierInvoiceNumber AS [رقم فاتورة المورد],
    s.SupplierName AS [اسم المورد],
    s.ContactPerson AS [جهة الاتصال],
    s.Phone AS [رقم الهاتف],
    pi.InvoiceDate AS [تاريخ الفاتورة],
    pi.DueDate AS [تاريخ الاستحقاق],
    pi.TotalAmount AS [إجمالي الفاتورة],
    pi.PaidAmount AS [المبلغ المدفوع],
    pi.RemainingAmount AS [المبلغ المتبقي],
    DateDiff("d", pi.DueDate, Date()) AS [أيام التأخير],
    IIF(pi.DueDate < Date(), "متأخرة", 
        IIF(pi.DueDate = Date(), "مستحقة اليوم", "غير مستحقة")) AS [حالة الاستحقاق],
    IIF(DateDiff("d", pi.DueDate, Date()) > 30, "خطر عالي",
        IIF(DateDiff("d", pi.DueDate, Date()) > 7, "خطر متوسط", "عادي")) AS [مستوى الخطر],
    cur.CurrencyName AS [العملة]
FROM (PurchaseInvoices pi 
LEFT JOIN Suppliers s ON pi.SupplierID = s.SupplierID)
LEFT JOIN Currencies cur ON pi.CurrencyID = cur.CurrencyID
WHERE pi.InvoiceStatus = 'Open' AND pi.RemainingAmount > 0
ORDER BY DateDiff("d", pi.DueDate, Date()) DESC, pi.RemainingAmount DESC;

-- 8. استعلام تحليل الأرباح العربي
-- ===============================
-- اسم الاستعلام: استعلام_تحليل_الأرباح
SELECT 
    p.ProductName AS [اسم المنتج],
    c.CategoryName AS [فئة المنتج],
    u.UnitName AS [وحدة القياس],
    p.SalePrice AS [سعر البيع],
    p.ProductionCost AS [تكلفة الإنتاج],
    (p.SalePrice - p.ProductionCost) AS [هامش الربح],
    IIF(p.ProductionCost > 0, ((p.SalePrice - p.ProductionCost) / p.ProductionCost) * 100, 0) AS [نسبة الربح %],
    IIF(inv.AvailableQuantity IS NULL, 0, inv.AvailableQuantity) AS [الكمية المتاحة],
    (IIF(inv.AvailableQuantity IS NULL, 0, inv.AvailableQuantity) * p.SalePrice) AS [قيمة المخزون بسعر البيع],
    (IIF(inv.AvailableQuantity IS NULL, 0, inv.AvailableQuantity) * p.ProductionCost) AS [قيمة المخزون بالتكلفة],
    ((IIF(inv.AvailableQuantity IS NULL, 0, inv.AvailableQuantity) * p.SalePrice) - 
     (IIF(inv.AvailableQuantity IS NULL, 0, inv.AvailableQuantity) * p.ProductionCost)) AS [الربح المتوقع من المخزون],
    p.MinimumLevel AS [الحد الأدنى],
    IIF(IIF(inv.AvailableQuantity IS NULL, 0, inv.AvailableQuantity) <= p.MinimumLevel, "تحت الحد الأدنى", "مناسب") AS [حالة المخزون],
    IIF(p.IsActive, "نشط", "غير نشط") AS [حالة المنتج]
FROM ((Products p 
LEFT JOIN Categories c ON p.CategoryID = c.CategoryID)
LEFT JOIN Units u ON p.UnitID = u.UnitID)
LEFT JOIN FinishedProductsInventory inv ON p.ProductID = inv.ProductID
WHERE p.IsActive = True
ORDER BY [نسبة الربح %] DESC;

-- 9. استعلام حركات المخزون العربي
-- ================================
-- اسم الاستعلام: استعلام_حركات_المخزون
SELECT 
    im.MovementDate AS [تاريخ الحركة],
    IIF(im.MovementType = 'Purchase', 'شراء',
        IIF(im.MovementType = 'Production', 'إنتاج',
            IIF(im.MovementType = 'Sale', 'بيع',
                IIF(im.MovementType = 'Adjustment', 'تسوية', im.MovementType)))) AS [نوع الحركة],
    IIF(im.ItemType = 'Raw Material', 'مادة خام', 'منتج نهائي') AS [نوع المادة],
    IIF(im.ItemType = 'Raw Material', rm.MaterialName, p.ProductName) AS [اسم المادة/المنتج],
    IIF(im.ItemType = 'Raw Material', c1.CategoryName, c2.CategoryName) AS [الفئة],
    IIF(im.ItemType = 'Raw Material', u1.UnitName, u2.UnitName) AS [وحدة القياس],
    im.Quantity AS [الكمية],
    im.UnitPrice AS [سعر الوحدة],
    im.TotalValue AS [إجمالي القيمة],
    im.BalanceBefore AS [الرصيد قبل الحركة],
    im.BalanceAfter AS [الرصيد بعد الحركة],
    (im.BalanceAfter - im.BalanceBefore) AS [صافي التغيير],
    IIF(im.ReferenceType = 'Purchase Invoice', 'فاتورة شراء',
        IIF(im.ReferenceType = 'Production Order', 'أمر إنتاج',
            IIF(im.ReferenceType = 'Sale Invoice', 'فاتورة بيع', im.ReferenceType))) AS [نوع المرجع],
    im.ReferenceNumber AS [رقم المرجع],
    im.Notes AS [ملاحظات],
    im.EntryUser AS [المستخدم]
FROM ((((InventoryMovements im 
LEFT JOIN RawMaterials rm ON im.ItemID = rm.MaterialID AND im.ItemType = 'Raw Material')
LEFT JOIN Products p ON im.ItemID = p.ProductID AND im.ItemType = 'Finished Product')
LEFT JOIN Categories c1 ON rm.CategoryID = c1.CategoryID)
LEFT JOIN Categories c2 ON p.CategoryID = c2.CategoryID)
LEFT JOIN Units u1 ON rm.UnitID = u1.UnitID
LEFT JOIN Units u2 ON p.UnitID = u2.UnitID
ORDER BY im.MovementDate DESC, im.MovementID DESC;

-- 10. استعلام ملخص الموردين العربي
-- =================================
-- اسم الاستعلام: استعلام_ملخص_الموردين
SELECT 
    s.SupplierName AS [اسم المورد],
    s.ContactPerson AS [جهة الاتصال],
    s.Phone AS [الهاتف],
    s.Email AS [البريد الإلكتروني],
    s.City AS [المدينة],
    s.CreditLimit AS [حد الائتمان],
    s.AccountBalance AS [رصيد الحساب],
    (s.CreditLimit - s.AccountBalance) AS [الائتمان المتاح],
    IIF(s.AccountBalance > s.CreditLimit, "تجاوز الحد", "ضمن الحد") AS [حالة الائتمان],
    COUNT(pi.InvoiceID) AS [عدد الفواتير],
    IIF(SUM(pi.TotalAmount) IS NULL, 0, SUM(pi.TotalAmount)) AS [إجمالي المشتريات],
    IIF(SUM(pi.PaidAmount) IS NULL, 0, SUM(pi.PaidAmount)) AS [إجمالي المدفوع],
    IIF(SUM(pi.RemainingAmount) IS NULL, 0, SUM(pi.RemainingAmount)) AS [إجمالي المستحق],
    IIF(COUNT(pi.InvoiceID) > 0, 
        IIF(SUM(pi.TotalAmount) IS NULL, 0, SUM(pi.TotalAmount)) / COUNT(pi.InvoiceID), 0) AS [متوسط قيمة الفاتورة],
    MAX(pi.InvoiceDate) AS [تاريخ آخر فاتورة],
    s.RegistrationDate AS [تاريخ التسجيل],
    IIF(s.IsActive, "نشط", "غير نشط") AS [الحالة]
FROM Suppliers s 
LEFT JOIN PurchaseInvoices pi ON s.SupplierID = pi.SupplierID
GROUP BY s.SupplierID, s.SupplierName, s.ContactPerson, s.Phone, s.Email, 
         s.City, s.CreditLimit, s.AccountBalance, s.RegistrationDate, s.IsActive
ORDER BY [إجمالي المشتريات] DESC;
