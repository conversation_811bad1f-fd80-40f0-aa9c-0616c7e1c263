-- نظام محاسبة مصنع المعمول والمخللات
-- إنشاء الجداول الأساسية

-- جدول وحدات القياس
CREATE TABLE وحدات_القياس (
    رقم_الوحدة AUTOINCREMENT PRIMARY KEY,
    اسم_الوحدة TEXT(50) NOT NULL,
    الرمز TEXT(10),
    ملاحظات MEMO
);

-- جدول العملات
CREATE TABLE العملات (
    رقم_العملة AUTOINCREMENT PRIMARY KEY,
    اسم_العملة TEXT(50) NOT NULL,
    الرمز TEXT(10),
    سعر_الصرف CURRENCY DEFAULT 1,
    تاريخ_التحديث DATETIME DEFAULT Now()
);

-- جدول فئات المواد
CREATE TABLE فئات_المواد (
    رقم_الفئة AUTOINCREMENT PRIMARY KEY,
    اسم_الفئة TEXT(100) NOT NULL,
    وصف_الفئة MEMO,
    نوع_الفئة TEXT(20) -- مواد خام أو منتجات نهائية
);

-- جدول المواد الخام
CREATE TABLE المواد_الخام (
    رقم_المادة AUTOINCREMENT PRIMARY KEY,
    اسم_المادة TEXT(100) NOT NULL,
    رقم_الفئة LONG,
    رقم_الوحدة LONG,
    الحد_الأدنى DOUBLE DEFAULT 0,
    متوسط_السعر CURRENCY DEFAULT 0,
    ملاحظات MEMO,
    تاريخ_الإنشاء DATETIME DEFAULT Now(),
    نشط YES/NO DEFAULT True,
    FOREIGN KEY (رقم_الفئة) REFERENCES فئات_المواد(رقم_الفئة),
    FOREIGN KEY (رقم_الوحدة) REFERENCES وحدات_القياس(رقم_الوحدة)
);

-- جدول الموردين
CREATE TABLE الموردين (
    رقم_المورد AUTOINCREMENT PRIMARY KEY,
    اسم_المورد TEXT(100) NOT NULL,
    اسم_جهة_الاتصال TEXT(100),
    الهاتف TEXT(20),
    الجوال TEXT(20),
    البريد_الإلكتروني TEXT(100),
    العنوان MEMO,
    المدينة TEXT(50),
    الدولة TEXT(50),
    الرقم_الضريبي TEXT(50),
    شروط_الدفع TEXT(100),
    مدة_الائتمان INTEGER DEFAULT 0,
    حد_الائتمان CURRENCY DEFAULT 0,
    رصيد_الحساب CURRENCY DEFAULT 0,
    تاريخ_التسجيل DATETIME DEFAULT Now(),
    نشط YES/NO DEFAULT True,
    ملاحظات MEMO
);

-- جدول فواتير الشراء
CREATE TABLE فواتير_الشراء (
    رقم_الفاتورة AUTOINCREMENT PRIMARY KEY,
    رقم_فاتورة_المورد TEXT(50),
    رقم_المورد LONG NOT NULL,
    تاريخ_الفاتورة DATETIME DEFAULT Now(),
    تاريخ_الاستحقاق DATETIME,
    رقم_العملة LONG DEFAULT 1,
    سعر_الصرف CURRENCY DEFAULT 1,
    إجمالي_قبل_الضريبة CURRENCY DEFAULT 0,
    قيمة_الضريبة CURRENCY DEFAULT 0,
    نسبة_الضريبة DOUBLE DEFAULT 0,
    إجمالي_الفاتورة CURRENCY DEFAULT 0,
    المبلغ_المدفوع CURRENCY DEFAULT 0,
    المبلغ_المتبقي CURRENCY DEFAULT 0,
    حالة_الفاتورة TEXT(20) DEFAULT 'مفتوحة',
    ملاحظات MEMO,
    تاريخ_الإدخال DATETIME DEFAULT Now(),
    مستخدم_الإدخال TEXT(50),
    FOREIGN KEY (رقم_المورد) REFERENCES الموردين(رقم_المورد),
    FOREIGN KEY (رقم_العملة) REFERENCES العملات(رقم_العملة)
);

-- جدول تفاصيل فواتير الشراء
CREATE TABLE تفاصيل_فواتير_الشراء (
    رقم_التفصيل AUTOINCREMENT PRIMARY KEY,
    رقم_الفاتورة LONG NOT NULL,
    رقم_المادة LONG NOT NULL,
    الكمية DOUBLE NOT NULL,
    سعر_الوحدة CURRENCY NOT NULL,
    إجمالي_السطر CURRENCY,
    نسبة_الخصم DOUBLE DEFAULT 0,
    قيمة_الخصم CURRENCY DEFAULT 0,
    الصافي CURRENCY,
    تاريخ_الانتهاء DATETIME,
    رقم_اللوط TEXT(50),
    ملاحظات MEMO,
    FOREIGN KEY (رقم_الفاتورة) REFERENCES فواتير_الشراء(رقم_الفاتورة),
    FOREIGN KEY (رقم_المادة) REFERENCES المواد_الخام(رقم_المادة)
);

-- جدول المنتجات النهائية
CREATE TABLE المنتجات (
    رقم_المنتج AUTOINCREMENT PRIMARY KEY,
    اسم_المنتج TEXT(100) NOT NULL,
    رقم_الفئة LONG,
    رقم_الوحدة LONG,
    سعر_البيع CURRENCY DEFAULT 0,
    تكلفة_الإنتاج CURRENCY DEFAULT 0,
    وقت_الإنتاج INTEGER DEFAULT 0, -- بالدقائق
    الحد_الأدنى DOUBLE DEFAULT 0,
    وصف_المنتج MEMO,
    تاريخ_الإنشاء DATETIME DEFAULT Now(),
    نشط YES/NO DEFAULT True,
    FOREIGN KEY (رقم_الفئة) REFERENCES فئات_المواد(رقم_الفئة),
    FOREIGN KEY (رقم_الوحدة) REFERENCES وحدات_القياس(رقم_الوحدة)
);

-- جدول وصفات الإنتاج (المواد الخام المطلوبة لكل منتج)
CREATE TABLE وصفات_الإنتاج (
    رقم_الوصفة AUTOINCREMENT PRIMARY KEY,
    رقم_المنتج LONG NOT NULL,
    رقم_المادة LONG NOT NULL,
    الكمية_المطلوبة DOUBLE NOT NULL,
    تكلفة_المادة CURRENCY DEFAULT 0,
    ملاحظات MEMO,
    FOREIGN KEY (رقم_المنتج) REFERENCES المنتجات(رقم_المنتج),
    FOREIGN KEY (رقم_المادة) REFERENCES المواد_الخام(رقم_المادة)
);

-- جدول مراحل التصنيع
CREATE TABLE مراحل_التصنيع (
    رقم_المرحلة AUTOINCREMENT PRIMARY KEY,
    اسم_المرحلة TEXT(100) NOT NULL,
    وصف_المرحلة MEMO,
    ترتيب_المرحلة INTEGER,
    تكلفة_المرحلة CURRENCY DEFAULT 0,
    وقت_المرحلة INTEGER DEFAULT 0 -- بالدقائق
);

-- جدول مراحل المنتجات (ربط المنتجات بمراحل التصنيع)
CREATE TABLE مراحل_المنتجات (
    رقم_السجل AUTOINCREMENT PRIMARY KEY,
    رقم_المنتج LONG NOT NULL,
    رقم_المرحلة LONG NOT NULL,
    ترتيب_المرحلة INTEGER,
    ملاحظات MEMO,
    FOREIGN KEY (رقم_المنتج) REFERENCES المنتجات(رقم_المنتج),
    FOREIGN KEY (رقم_المرحلة) REFERENCES مراحل_التصنيع(رقم_المرحلة)
);

-- جدول أوامر الإنتاج
CREATE TABLE أوامر_الإنتاج (
    رقم_الأمر AUTOINCREMENT PRIMARY KEY,
    رقم_المنتج LONG NOT NULL,
    الكمية_المطلوبة DOUBLE NOT NULL,
    الكمية_المنتجة DOUBLE DEFAULT 0,
    تاريخ_الأمر DATETIME DEFAULT Now(),
    تاريخ_البدء DATETIME,
    تاريخ_الانتهاء DATETIME,
    حالة_الأمر TEXT(20) DEFAULT 'جديد',
    التكلفة_المقدرة CURRENCY DEFAULT 0,
    التكلفة_الفعلية CURRENCY DEFAULT 0,
    ملاحظات MEMO,
    مستخدم_الإدخال TEXT(50),
    FOREIGN KEY (رقم_المنتج) REFERENCES المنتجات(رقم_المنتج)
);

-- جدول تفاصيل أوامر الإنتاج (استهلاك المواد الخام)
CREATE TABLE تفاصيل_أوامر_الإنتاج (
    رقم_التفصيل AUTOINCREMENT PRIMARY KEY,
    رقم_الأمر LONG NOT NULL,
    رقم_المادة LONG NOT NULL,
    الكمية_المطلوبة DOUBLE NOT NULL,
    الكمية_المستهلكة DOUBLE DEFAULT 0,
    تكلفة_الوحدة CURRENCY DEFAULT 0,
    إجمالي_التكلفة CURRENCY DEFAULT 0,
    تاريخ_الاستهلاك DATETIME,
    ملاحظات MEMO,
    FOREIGN KEY (رقم_الأمر) REFERENCES أوامر_الإنتاج(رقم_الأمر),
    FOREIGN KEY (رقم_المادة) REFERENCES المواد_الخام(رقم_المادة)
);

-- جدول مخزون المواد الخام
CREATE TABLE مخزون_المواد_الخام (
    رقم_السجل AUTOINCREMENT PRIMARY KEY,
    رقم_المادة LONG NOT NULL,
    الكمية_المتاحة DOUBLE DEFAULT 0,
    متوسط_التكلفة CURRENCY DEFAULT 0,
    إجمالي_القيمة CURRENCY DEFAULT 0,
    تاريخ_آخر_حركة DATETIME,
    الحد_الأدنى DOUBLE DEFAULT 0,
    الحد_الأقصى DOUBLE DEFAULT 0,
    نقطة_إعادة_الطلب DOUBLE DEFAULT 0,
    ملاحظات MEMO,
    FOREIGN KEY (رقم_المادة) REFERENCES المواد_الخام(رقم_المادة)
);

-- جدول مخزون المنتجات النهائية
CREATE TABLE مخزون_المنتجات_النهائية (
    رقم_السجل AUTOINCREMENT PRIMARY KEY,
    رقم_المنتج LONG NOT NULL,
    الكمية_المتاحة DOUBLE DEFAULT 0,
    متوسط_التكلفة CURRENCY DEFAULT 0,
    إجمالي_القيمة CURRENCY DEFAULT 0,
    تاريخ_آخر_حركة DATETIME,
    الحد_الأدنى DOUBLE DEFAULT 0,
    الحد_الأقصى DOUBLE DEFAULT 0,
    تاريخ_الانتهاء DATETIME,
    رقم_اللوط TEXT(50),
    ملاحظات MEMO,
    FOREIGN KEY (رقم_المنتج) REFERENCES المنتجات(رقم_المنتج)
);

-- جدول حركات المخزون
CREATE TABLE حركات_المخزون (
    رقم_الحركة AUTOINCREMENT PRIMARY KEY,
    نوع_الحركة TEXT(20) NOT NULL, -- دخول، خروج، تحويل، جرد
    نوع_المادة TEXT(20) NOT NULL, -- مادة خام، منتج نهائي
    رقم_المادة LONG NOT NULL,
    الكمية DOUBLE NOT NULL,
    سعر_الوحدة CURRENCY DEFAULT 0,
    إجمالي_القيمة CURRENCY DEFAULT 0,
    تاريخ_الحركة DATETIME DEFAULT Now(),
    رقم_المرجع LONG, -- رقم الفاتورة أو أمر الإنتاج
    نوع_المرجع TEXT(20), -- فاتورة شراء، أمر إنتاج، إلخ
    الرصيد_قبل_الحركة DOUBLE DEFAULT 0,
    الرصيد_بعد_الحركة DOUBLE DEFAULT 0,
    ملاحظات MEMO,
    مستخدم_الإدخال TEXT(50)
);

-- جدول المراكز التكلفة
CREATE TABLE مراكز_التكلفة (
    رقم_المركز AUTOINCREMENT PRIMARY KEY,
    اسم_المركز TEXT(100) NOT NULL,
    نوع_المركز TEXT(50), -- إنتاج، إداري، تسويق
    وصف_المركز MEMO,
    نشط YES/NO DEFAULT True
);

-- جدول تكاليف الإنتاج
CREATE TABLE تكاليف_الإنتاج (
    رقم_التكلفة AUTOINCREMENT PRIMARY KEY,
    رقم_الأمر LONG NOT NULL,
    رقم_المركز LONG,
    نوع_التكلفة TEXT(50), -- مواد مباشرة، عمالة، تكاليف غير مباشرة
    قيمة_التكلفة CURRENCY NOT NULL,
    تاريخ_التكلفة DATETIME DEFAULT Now(),
    وصف_التكلفة MEMO,
    مستخدم_الإدخال TEXT(50),
    FOREIGN KEY (رقم_الأمر) REFERENCES أوامر_الإنتاج(رقم_الأمر),
    FOREIGN KEY (رقم_المركز) REFERENCES مراكز_التكلفة(رقم_المركز)
);

-- إدراج البيانات الأساسية

-- وحدات القياس
INSERT INTO وحدات_القياس (اسم_الوحدة, الرمز) VALUES
('كيلوجرام', 'كجم'),
('جرام', 'جم'),
('لتر', 'لتر'),
('مليلتر', 'مل'),
('قطعة', 'قطعة'),
('علبة', 'علبة'),
('كيس', 'كيس'),
('صندوق', 'صندوق');

-- العملات
INSERT INTO العملات (اسم_العملة, الرمز, سعر_الصرف) VALUES
('الريال السعودي', 'ريال', 1.00),
('الدولار الأمريكي', '$', 3.75),
('اليورو', '€', 4.10);

-- فئات المواد
INSERT INTO فئات_المواد (اسم_الفئة, وصف_الفئة, نوع_الفئة) VALUES
('دقيق ونشويات', 'الدقيق والنشويات المستخدمة في التصنيع', 'مواد خام'),
('سكر ومحليات', 'السكر والمحليات الطبيعية والصناعية', 'مواد خام'),
('زيوت ودهون', 'الزيوت والدهون المستخدمة في الطبخ', 'مواد خام'),
('توابل وبهارات', 'التوابل والبهارات والنكهات', 'مواد خام'),
('خضروات وفواكه', 'الخضروات والفواكه الطازجة والمجففة', 'مواد خام'),
('مواد حافظة', 'المواد الحافظة والمثبتات', 'مواد خام'),
('عبوات وتغليف', 'العبوات ومواد التغليف', 'مواد خام'),
('معمول', 'أصناف المعمول المختلفة', 'منتجات نهائية'),
('مخللات', 'أصناف المخللات المختلفة', 'منتجات نهائية');

-- مراحل التصنيع
INSERT INTO مراحل_التصنيع (اسم_المرحلة, وصف_المرحلة, ترتيب_المرحلة, وقت_المرحلة) VALUES
('التحضير والخلط', 'تحضير وخلط المكونات الأساسية', 1, 30),
('العجن والتشكيل', 'عجن العجينة وتشكيلها', 2, 45),
('الحشو والتعبئة', 'حشو المعمول أو تعبئة المخللات', 3, 60),
('الطبخ والمعالجة', 'طبخ المعمول أو معالجة المخللات', 4, 90),
('التبريد والتجفيف', 'تبريد المنتج وتجفيفه', 5, 120),
('التغليف والتعبئة', 'تغليف المنتج النهائي', 6, 20),
('الفحص والجودة', 'فحص الجودة والتأكد من المواصفات', 7, 15),
('التخزين والشحن', 'تخزين المنتج وتحضيره للشحن', 8, 10);

-- مراكز التكلفة
INSERT INTO مراكز_التكلفة (اسم_المركز, نوع_المركز, وصف_المركز) VALUES
('قسم إنتاج المعمول', 'إنتاج', 'قسم متخصص في إنتاج جميع أنواع المعمول'),
('قسم إنتاج المخللات', 'إنتاج', 'قسم متخصص في إنتاج وتعليب المخللات'),
('قسم التغليف والتعبئة', 'إنتاج', 'قسم التغليف النهائي للمنتجات'),
('قسم مراقبة الجودة', 'إنتاج', 'قسم فحص ومراقبة جودة المنتجات'),
('الإدارة العامة', 'إداري', 'التكاليف الإدارية العامة'),
('قسم المبيعات والتسويق', 'تسويق', 'تكاليف المبيعات والتسويق');
